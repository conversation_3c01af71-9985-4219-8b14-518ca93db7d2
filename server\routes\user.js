const express = require('express');
const jwt = require('jsonwebtoken');

// Use appropriate database based on configuration
const database = process.env.USE_SUPABASE === 'true'
    ? require('../supabase')
    : require('../database');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Middleware to authenticate JWT token
function authenticateToken(req, res, next) {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
        return res.status(401).json({ 
            message: 'Access token required' 
        });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ 
                message: 'Invalid or expired token' 
            });
        }

        req.user = user;
        next();
    });
}

// Get user profile
router.get('/profile', authenticateToken, (req, res) => {
    const userId = req.user.userId;

    database.getUserById(userId, (err, user) => {
        if (err) {return res.status(500).json({ 
                message: 'Internal server error' 
            });
        }

        if (!user) {
            return res.status(404).json({ 
                message: 'User not found' 
            });
        }

        res.json({
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                planType: user.plan_type,
                createdAt: user.created_at
            }
        });
    });
});

// Update user profile
router.put('/profile', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const { name } = req.body;

    if (!name || name.trim().length === 0) {
        return res.status(400).json({ 
            message: 'Name is required' 
        });
    }

    database.db.run(
        'UPDATE users SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [name.trim(), userId],
        function(err) {
            if (err) {return res.status(500).json({ 
                    message: 'Failed to update profile' 
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({ 
                    message: 'User not found' 
                });
            }

            res.json({ 
                message: 'Profile updated successfully',
                user: {
                    name: name.trim()
                }
            });
        }
    );
});

// Get user preferences
router.get('/preferences', authenticateToken, (req, res) => {
    const userId = req.user.userId;

    database.db.all(
        'SELECT preference_key, preference_value FROM user_preferences WHERE user_id = ?',
        [userId],
        (err, preferences) => {
            if (err) {return res.status(500).json({ 
                    message: 'Internal server error' 
                });
            }

            // Convert array to object
            const preferencesObj = {};
            preferences.forEach(pref => {
                preferencesObj[pref.preference_key] = pref.preference_value;
            });

            res.json({ preferences: preferencesObj });
        }
    );
});

// Update user preferences
router.put('/preferences', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const { preferences } = req.body;

    if (!preferences || typeof preferences !== 'object') {
        return res.status(400).json({ 
            message: 'Preferences object is required' 
        });
    }

    // Begin transaction
    database.db.serialize(() => {
        database.db.run('BEGIN TRANSACTION');

        let hasError = false;
        const keys = Object.keys(preferences);
        let completed = 0;

        if (keys.length === 0) {
            database.db.run('COMMIT');
            return res.json({ message: 'No preferences to update' });
        }

        keys.forEach(key => {
            const value = preferences[key];
            
            database.db.run(
                `INSERT OR REPLACE INTO user_preferences 
                 (user_id, preference_key, preference_value, updated_at) 
                 VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
                [userId, key, value],
                function(err) {
                    completed++;
                    
                    if (err && !hasError) {
                        hasError = true;database.db.run('ROLLBACK');
                        return res.status(500).json({ 
                            message: 'Failed to update preferences' 
                        });
                    }

                    if (completed === keys.length && !hasError) {
                        database.db.run('COMMIT');
                        res.json({ 
                            message: 'Preferences updated successfully' 
                        });
                    }
                }
            );
        });
    });
});

// Get user dashboard summary
router.get('/dashboard-summary', authenticateToken, (req, res) => {
    const userId = req.user.userId;

    // Get accounts summary
    database.getUserAccounts(userId, (err, accounts) => {
        if (err) {return res.status(500).json({ 
                message: 'Internal server error' 
            });
        }

        // Calculate totals
        let totalAssets = 0;
        let totalLiabilities = 0;

        accounts.forEach(account => {
            if (account.balance > 0) {
                totalAssets += account.balance;
            } else {
                totalLiabilities += Math.abs(account.balance);
            }
        });

        const netWorth = totalAssets - totalLiabilities;

        // Get recent transactions
        database.getUserTransactions(userId, 10, (err, transactions) => {
            if (err) {return res.status(500).json({ 
                    message: 'Internal server error' 
                });
            }

            // Get goals
            database.getUserGoals(userId, (err, goals) => {
                if (err) {return res.status(500).json({ 
                        message: 'Internal server error' 
                    });
                }

                // Calculate monthly income and expenses from recent transactions
                const currentMonth = new Date().getMonth();
                const currentYear = new Date().getFullYear();
                
                let monthlyIncome = 0;
                let monthlyExpenses = 0;

                transactions.forEach(transaction => {
                    const transactionDate = new Date(transaction.transaction_date);
                    if (transactionDate.getMonth() === currentMonth && 
                        transactionDate.getFullYear() === currentYear) {
                        if (transaction.amount > 0) {
                            monthlyIncome += transaction.amount;
                        } else {
                            monthlyExpenses += Math.abs(transaction.amount);
                        }
                    }
                });

                const savingsRate = monthlyIncome > 0 ? 
                    ((monthlyIncome - monthlyExpenses) / monthlyIncome * 100) : 0;

                res.json({
                    summary: {
                        netWorth: netWorth,
                        totalAssets: totalAssets,
                        totalLiabilities: totalLiabilities,
                        monthlyIncome: monthlyIncome,
                        monthlyExpenses: monthlyExpenses,
                        savingsRate: Math.round(savingsRate * 100) / 100,
                        accountsCount: accounts.length,
                        goalsCount: goals.length,
                        activeGoals: goals.filter(g => g.status === 'active').length
                    },
                    accounts: accounts,
                    recentTransactions: transactions.slice(0, 5),
                    goals: goals
                });
            });
        });
    });
});

// Update user plan
router.put('/plan', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const { planType } = req.body;

    const validPlans = ['basic', 'pro', 'premium'];
    if (!planType || !validPlans.includes(planType)) {
        return res.status(400).json({ 
            message: 'Valid plan type is required (basic, pro, premium)' 
        });
    }

    database.db.run(
        'UPDATE users SET plan_type = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [planType, userId],
        function(err) {
            if (err) {return res.status(500).json({ 
                    message: 'Failed to update plan' 
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({ 
                    message: 'User not found' 
                });
            }

            res.json({ 
                message: 'Plan updated successfully',
                planType: planType
            });
        }
    );
});

// Delete user account
router.delete('/account', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const { confirmPassword } = req.body;

    if (!confirmPassword) {
        return res.status(400).json({ 
            message: 'Password confirmation is required' 
        });
    }

    // Get user to verify password
    database.getUserById(userId, async (err, user) => {
        if (err || !user) {
            return res.status(404).json({ 
                message: 'User not found' 
            });
        }

        // Verify password
        const bcrypt = require('bcryptjs');
        const isValidPassword = await bcrypt.compare(confirmPassword, user.password_hash);
        
        if (!isValidPassword) {
            return res.status(401).json({ 
                message: 'Invalid password' 
            });
        }

        // Begin transaction to delete all user data
        database.db.serialize(() => {
            database.db.run('BEGIN TRANSACTION');

            const tables = ['chat_history', 'insights', 'user_preferences', 'goals', 'transactions', 'accounts', 'users'];
            let completed = 0;
            let hasError = false;

            tables.forEach(table => {
                database.db.run(
                    `DELETE FROM ${table} WHERE user_id = ?`,
                    [userId],
                    function(err) {
                        completed++;
                        
                        if (err && !hasError) {
                            hasError = true;database.db.run('ROLLBACK');
                            return res.status(500).json({ 
                                message: 'Failed to delete account' 
                            });
                        }

                        if (completed === tables.length && !hasError) {
                            database.db.run('COMMIT');
                            res.json({ 
                                message: 'Account deleted successfully' 
                            });
                        }
                    }
                );
            });
        });
    });
});

// Get user activity log (placeholder for future implementation)
router.get('/activity', authenticateToken, (req, res) => {
    // This would typically return user activity logs
    res.json({
        activities: [
            {
                id: 1,
                type: 'login',
                description: 'User logged in',
                timestamp: new Date().toISOString()
            }
        ]
    });
});

module.exports = router;
