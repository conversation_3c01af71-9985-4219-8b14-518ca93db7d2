// Test API endpoints
const https = require('https');
const http = require('http');

function makeRequest(url, options) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https:') ? https : http;

        const req = protocol.request(url, options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, json: () => jsonData, statusCode: res.statusCode });
                } catch (e) {
                    resolve({ ok: false, json: () => ({ error: data }), statusCode: res.statusCode });
                }
            });
        });

        req.on('error', reject);

        if (options.body) {
            req.write(options.body);
        }

        req.end();
    });
}

async function testLogin() {
    try {
        console.log('Testing login API...');
        
        const response = await makeRequest('http://localhost:3001/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'demo123'
            })
        });

        const data = await response.json();
        console.log('Login response:', data);

        if (response.ok) {
            console.log('✅ Login successful!');
            return data.token;
        } else {
            console.log('❌ Login failed:', data.message);
        }
    } catch (error) {
        console.error('❌ Login error:', error.message);
    }
}

async function testSignup() {
    try {
        console.log('Testing signup API...');
        
        const response = await makeRequest('http://localhost:3001/api/auth/signup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: 'Test User',
                email: '<EMAIL>',
                password: 'test123'
            })
        });

        const data = await response.json();
        console.log('Signup response:', data);

        if (response.ok) {
            console.log('✅ Signup successful!');
        } else {
            console.log('❌ Signup failed:', data.message);
        }
    } catch (error) {
        console.error('❌ Signup error:', error.message);
    }
}

async function testHealth() {
    try {
        console.log('Testing health endpoint...');
        
        const response = await makeRequest('http://localhost:3001/api/health', { method: 'GET' });
        const data = await response.json();
        
        console.log('Health response:', data);
        
        if (response.ok) {
            console.log('✅ Server is healthy!');
        } else {
            console.log('❌ Server health check failed');
        }
    } catch (error) {
        console.error('❌ Health check error:', error.message);
    }
}

async function runTests() {
    console.log('🧪 Running API Tests...\n');
    
    await testHealth();
    console.log('');
    
    await testLogin();
    console.log('');
    
    await testSignup();
    console.log('');
    
    console.log('🏁 Tests completed!');
}

runTests();
