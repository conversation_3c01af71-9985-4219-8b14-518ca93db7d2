<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test - WealthWise AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 16px;
        }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #5855eb;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔐 Login Test - WealthWise AI</h1>
        <p>This page tests the login functionality directly in the browser.</p>

        <form id="testLoginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="demo123" required>
            </div>
            
            <button type="submit">Test Login</button>
            <button type="button" onclick="testHealthCheck()">Test Health Check</button>
            <button type="button" onclick="testSignup()">Test Signup</button>
            <button type="button" onclick="clearResults()">Clear Results</button>
        </form>

        <div id="result" class="result info">
Ready to test login functionality.
Current URL: <span id="currentUrl"></span>
Server URL: http://localhost:3002
        </div>
    </div>

    <script>
        // Display current URL
        document.getElementById('currentUrl').textContent = window.location.href;

        // Test login function
        async function testLogin(email, password) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Testing login...';

            try {
                console.log('Starting login test...');
                console.log('Email:', email);
                console.log('Password:', password ? '***' : 'empty');

                const requestBody = { email, password };
                console.log('Request body:', requestBody);

                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('Response received:', response);
                console.log('Status:', response.status);
                console.log('Status Text:', response.statusText);
                console.log('Headers:', response.headers);

                const data = await response.json();
                console.log('Response data:', data);

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ LOGIN SUCCESS!
Status: ${response.status}
User: ${data.user.name}
Email: ${data.user.email}
Plan: ${data.user.planType}
Token: ${data.token ? 'Generated' : 'Missing'}
Message: ${data.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ LOGIN FAILED
Status: ${response.status}
Message: ${data.message}
Error: ${data.error || 'Unknown error'}`;
                }
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ NETWORK ERROR
Error: ${error.message}
Stack: ${error.stack}

This usually means:
- Server is not running
- CORS issues
- Network connectivity problems
- JavaScript errors`;
            }
        }

        // Test health check
        async function testHealthCheck() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Testing health check...';

            try {
                const response = await fetch('/api/health');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ HEALTH CHECK SUCCESS!
Status: ${data.status}
Version: ${data.version}
Timestamp: ${data.timestamp}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ HEALTH CHECK FAILED
Status: ${response.status}
Response: ${JSON.stringify(data)}`;
                }
            } catch (error) {
                console.error('Health check error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ HEALTH CHECK ERROR
Error: ${error.message}`;
            }
        }

        // Test signup
        async function testSignup() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Testing signup...';

            try {
                const signupData = {
                    name: 'Test User',
                    email: 'test' + Date.now() + '@example.com',
                    password: 'testpass123'
                };

                const response = await fetch('/api/auth/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(signupData)
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ SIGNUP SUCCESS!
Status: ${response.status}
User: ${data.user.name}
Email: ${data.user.email}
Token: ${data.token ? 'Generated' : 'Missing'}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ SIGNUP FAILED
Status: ${response.status}
Message: ${data.message}`;
                }
            } catch (error) {
                console.error('Signup error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ SIGNUP ERROR
Error: ${error.message}`;
            }
        }

        // Clear results
        function clearResults() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Results cleared. Ready for new test.';
        }

        // Form submission handler
        document.getElementById('testLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Please fill in both email and password';
                return;
            }
            
            await testLogin(email, password);
        });

        // Auto-test health check on page load
        window.addEventListener('load', function() {
            console.log('Page loaded, testing health check...');
            setTimeout(testHealthCheck, 1000);
        });
    </script>
</body>
</html>
