# WealthWise AI - Supabase Integration Guide

## 🎉 Supabase Database Setup Complete!

WealthWise AI has been successfully integrated with Supabase, providing a robust, scalable PostgreSQL database with built-in authentication, real-time capabilities, and Row Level Security (RLS).

## 📊 Database Schema Created

### Tables Created in Supabase:

1. **users** - User accounts and authentication
   - `id` (UUID, Primary Key)
   - `name` (TEXT)
   - `email` (TEXT, Unique)
   - `password_hash` (TEXT)
   - `plan_type` (TEXT: basic, pro, premium)
   - `created_at`, `updated_at` (TIMESTAMPTZ)

2. **accounts** - Financial accounts
   - `id` (UUID, Primary Key)
   - `user_id` (UUID, Foreign Key)
   - `account_type` (TEXT: checking, savings, investment, credit, loan, other)
   - `account_name` (TEXT)
   - `institution` (TEXT)
   - `balance` (DECIMAL)
   - `currency` (TEXT, default: USD)
   - `is_active` (BOOLEAN)

3. **transactions** - Financial transactions
   - `id` (UUID, Primary Key)
   - `user_id` (UUID, Foreign Key)
   - `account_id` (UUID, Foreign Key)
   - `amount` (DECIMAL)
   - `description` (TEXT)
   - `category` (TEXT)
   - `transaction_date` (DATE)

4. **goals** - Financial goals
   - `id` (UUID, Primary Key)
   - `user_id` (UUID, Foreign Key)
   - `name` (TEXT)
   - `target_amount` (DECIMAL)
   - `current_amount` (DECIMAL)
   - `target_date` (DATE)
   - `category` (TEXT: emergency, house, car, vacation, retirement, education, other)
   - `status` (TEXT: active, completed, paused, cancelled)

5. **chat_history** - AI chat conversations
   - `id` (UUID, Primary Key)
   - `user_id` (UUID, Foreign Key)
   - `message` (TEXT)
   - `response` (TEXT)
   - `created_at` (TIMESTAMPTZ)

6. **user_preferences** - User settings
   - `id` (UUID, Primary Key)
   - `user_id` (UUID, Foreign Key)
   - `preference_key` (TEXT)
   - `preference_value` (TEXT)

7. **insights** - AI-generated financial insights
   - `id` (UUID, Primary Key)
   - `user_id` (UUID, Foreign Key)
   - `insight_type` (TEXT)
   - `title` (TEXT)
   - `description` (TEXT)
   - `action_items` (JSONB)
   - `priority` (INTEGER)
   - `is_read` (BOOLEAN)

8. **scenarios** - Financial scenario planning
   - `id` (UUID, Primary Key)
   - `user_id` (UUID, Foreign Key)
   - `name` (TEXT)
   - `scenario_type` (TEXT)
   - `parameters` (JSONB)
   - `results` (JSONB)

9. **notifications** - User notifications
   - `id` (UUID, Primary Key)
   - `user_id` (UUID, Foreign Key)
   - `title` (TEXT)
   - `message` (TEXT)
   - `type` (TEXT: info, warning, success, error)
   - `is_read` (BOOLEAN)

10. **audit_logs** - Security and activity tracking
    - `id` (UUID, Primary Key)
    - `user_id` (UUID, Foreign Key)
    - `action` (TEXT)
    - `resource_type` (TEXT)
    - `resource_id` (UUID)
    - `details` (JSONB)
    - `ip_address` (INET)
    - `user_agent` (TEXT)

## 🔐 Security Features Implemented

### Row Level Security (RLS)
All tables have RLS enabled with policies ensuring users can only access their own data:

- **Users**: Can view and update their own profile
- **Accounts**: Full CRUD access to own accounts only
- **Transactions**: Full CRUD access to own transactions only
- **Goals**: Full CRUD access to own goals only
- **Chat History**: Can view and insert own chat history
- **Preferences**: Full CRUD access to own preferences
- **Insights**: Can view and update own insights
- **Scenarios**: Full CRUD access to own scenarios
- **Notifications**: Can view and update own notifications
- **Audit Logs**: Can view own logs, service role can insert

### Database Indexes
Optimized indexes created for:
- User lookups by email
- Account queries by user_id and type
- Transaction queries by user_id, date, and category
- Goal queries by user_id, status, and category
- Chat history by user_id and timestamp
- All foreign key relationships

## 📝 Sample Data

Sample data has been created including:
- Demo user: `<EMAIL>` / `demo123`
- Sample accounts (checking, savings, investment, credit)
- Sample financial goals (emergency fund, house down payment, retirement)
- Sample transactions (income, expenses across categories)

## 🚀 Getting Started with Supabase

### Step 1: Get Your API Keys
1. Go to [Supabase Dashboard](https://supabase.com/dashboard/project/hkvhlqyyxjwnhzldxnao/settings/api)
2. Copy your `anon public` key
3. Copy your `service_role` key (keep this secret!)

### Step 2: Update Environment Variables
```bash
# Update .env file
SUPABASE_URL=https://hkvhlqyyxjwnhzldxnao.supabase.co
SUPABASE_ANON_KEY=your_actual_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key_here
USE_SUPABASE=true
```

### Step 3: Install Supabase Client (Already Done)
```bash
npm install @supabase/supabase-js
```

### Step 4: Switch to Supabase
The application is configured to use either SQLite or Supabase based on the `USE_SUPABASE` environment variable.

## 🔄 Migration from SQLite to Supabase

The application supports both databases:
- **SQLite**: Local development and testing
- **Supabase**: Production-ready PostgreSQL with advanced features

### Database Abstraction Layer
The application uses a database abstraction layer that supports both callback and promise patterns, making it easy to switch between SQLite and Supabase.

## 🌟 Supabase Advantages

### 1. **Scalability**
- PostgreSQL database that scales with your application
- Built-in connection pooling and optimization

### 2. **Real-time Features**
- Real-time subscriptions for live data updates
- Perfect for collaborative financial planning

### 3. **Built-in Authentication**
- OAuth providers (Google, GitHub, etc.)
- Magic link authentication
- JWT token management

### 4. **Security**
- Row Level Security (RLS) policies
- Built-in user management
- Secure API endpoints

### 5. **Developer Experience**
- Auto-generated API documentation
- Built-in dashboard for data management
- SQL editor with syntax highlighting

### 6. **Advanced Features**
- Full-text search capabilities
- PostGIS for location-based features
- Edge functions for serverless computing

## 🔧 Development vs Production

### Development (Current Setup)
- Uses SQLite for local development
- Fast setup and testing
- No external dependencies

### Production (Supabase Ready)
- PostgreSQL database in the cloud
- Automatic backups and scaling
- Global CDN and edge locations

## 📚 Next Steps

1. **Get API Keys**: Obtain real Supabase API keys from the dashboard
2. **Enable Supabase**: Set `USE_SUPABASE=true` in environment
3. **Test Integration**: Verify all features work with Supabase
4. **Deploy**: Use Supabase for production deployment

## 🔗 Useful Links

- **Supabase Dashboard**: https://supabase.com/dashboard/project/hkvhlqyyxjwnhzldxnao
- **API Settings**: https://supabase.com/dashboard/project/hkvhlqyyxjwnhzldxnao/settings/api
- **Database Editor**: https://supabase.com/dashboard/project/hkvhlqyyxjwnhzldxnao/editor
- **Authentication**: https://supabase.com/dashboard/project/hkvhlqyyxjwnhzldxnao/auth/users

## 🎯 Benefits for WealthWise AI

1. **Multi-user Support**: Secure isolation between users
2. **Real-time Updates**: Live financial data synchronization
3. **Scalability**: Handle thousands of users
4. **Security**: Enterprise-grade data protection
5. **Analytics**: Built-in usage analytics and monitoring
6. **Backup**: Automatic daily backups
7. **Global**: Deploy globally with edge locations

The Supabase integration provides WealthWise AI with a production-ready, scalable database solution that can grow with the application's success!
