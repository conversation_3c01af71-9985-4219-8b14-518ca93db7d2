// Main Application JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Check if user is logged in
    const token = localStorage.getItem('authToken');
    if (token && window.location.pathname === '/index.html') {
        // Redirect to dashboard if already logged in
        window.location.href = 'dashboard.html';
    }
    
    // Initialize event listeners
    initializeEventListeners();
    
    // Initialize animations
    initializeAnimations();
}

function initializeEventListeners() {
    // Modal event listeners
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            closeModal(event.target.id);
        }
    });
    
    // Form submissions are handled in auth.js
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

function initializeAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.feature-card, .pricing-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Modal Functions
function showLoginModal() {
    const modal = document.getElementById('loginModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function showSignupModal() {
    const modal = document.getElementById('signupModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function showContactSalesModal() {
    const modal = document.getElementById('contactSalesModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function startFreeTrial() {
    // For Pro plan, show signup modal with a special indicator
    showSignupModal();

    // Add a visual indicator that this is for the Pro plan
    const signupModal = document.getElementById('signupModal');
    if (signupModal) {
        const header = signupModal.querySelector('h2');
        if (header) {
            header.textContent = 'Start Your Free Pro Trial';
            header.style.color = '#6366f1';
        }

        // Add a note about the trial
        let trialNote = signupModal.querySelector('.trial-note');
        if (!trialNote) {
            trialNote = document.createElement('p');
            trialNote.className = 'trial-note';
            trialNote.style.cssText = 'color: #6366f1; font-size: 14px; margin: 10px 0; text-align: center;';
            trialNote.textContent = '🎉 Start your 14-day free trial of WealthWise Pro!';
            header.insertAdjacentElement('afterend', trialNote);
        }
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    document.body.style.overflow = 'auto';

    // Reset signup modal header if it was modified for free trial
    if (modalId === 'signupModal') {
        const signupModal = document.getElementById('signupModal');
        const header = signupModal.querySelector('h2');
        const trialNote = signupModal.querySelector('.trial-note');

        if (header) {
            header.textContent = 'Create Your Account';
            header.style.color = '';
        }

        if (trialNote) {
            trialNote.remove();
        }
    }
}

function showDemo() {
    // Simulate demo functionality
    alert('Demo feature coming soon! Sign up for early access.');
}

// Form Handlers are now in auth.js

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#2563eb'};
        color: white;
        padding: 16px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        max-width: 400px;
        animation: slideIn 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Add notification styles
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .notification-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12px;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
`;
document.head.appendChild(notificationStyles);

// API Helper Functions
function getAuthHeaders() {
    const token = localStorage.getItem('authToken');
    return {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
    };
}

async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: getAuthHeaders(),
        ...options
    };
    
    try {
        const response = await fetch(url, defaultOptions);
        
        if (response.status === 401) {
            // Token expired, redirect to login
            localStorage.removeItem('authToken');
            localStorage.removeItem('userData');
            window.location.href = 'index.html';
            return;
        }
        
        return await response.json();
    } catch (error) {
        throw error;
    }
}

// Export functions for use in other files
window.WealthWiseApp = {
    showNotification,
    apiRequest,
    getAuthHeaders,
    closeModal
};
