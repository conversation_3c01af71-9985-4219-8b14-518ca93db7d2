{"version": 3, "file": "headers.js", "sourceRoot": "", "sources": ["../src/internal/headers.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;AAWtF,MAAM,4BAA4B,GAAG,MAAM,CAAC,GAAG,CAAC,8BAA8B,CAE7E,CAAC;AAgBF,MAAM,OAAO,GAAG,KAAK,CAAC,OAAsD,CAAC;AAE7E,QAAQ,CAAC,CAAC,cAAc,CAAC,OAAoB;IAC3C,IAAI,CAAC,OAAO;QAAE,OAAO;IAErB,IAAI,4BAA4B,IAAI,OAAO,EAAE,CAAC;QAC5C,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAA0B,CAAC;QACrD,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACxB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACrB,CAAC;QACD,OAAO;IACT,CAAC;IAED,IAAI,WAAW,GAAG,KAAK,CAAC;IACxB,IAAI,IAAiE,CAAC;IACtE,IAAI,OAAO,YAAY,OAAO,EAAE,CAAC;QAC/B,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;SAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5B,IAAI,GAAG,OAAO,CAAC;IACjB,CAAC;SAAM,CAAC;QACN,WAAW,GAAG,IAAI,CAAC;QACnB,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;IACvC,CAAC;IACD,KAAK,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACrB,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,OAAO,IAAI,KAAK,QAAQ;YAAE,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;QACzF,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,KAAK,KAAK,SAAS;gBAAE,SAAS;YAElC,kEAAkE;YAClE,iEAAiE;YACjE,IAAI,WAAW,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC7B,QAAQ,GAAG,IAAI,CAAC;gBAChB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACrB,CAAC;YACD,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;AACH,CAAC;AAEM,MAAM,YAAY,GAAG,CAAC,UAAyB,EAAmB,EAAE;IACzE,MAAM,aAAa,GAAG,IAAI,OAAO,EAAE,CAAC;IACpC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;IACtC,KAAK,MAAM,OAAO,IAAI,UAAU,EAAE,CAAC;QACjC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QACtC,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YACpD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBAChC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC3B,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACnB,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC3B,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAClC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC,4BAA4B,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC7F,CAAC,CAAC;AArBW,QAAA,YAAY,gBAqBvB;AAEK,MAAM,cAAc,GAAG,CAAC,OAAoB,EAAE,EAAE;IACrD,KAAK,MAAM,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC;QAAE,OAAO,KAAK,CAAC;IACtD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAHW,QAAA,cAAc,kBAGzB"}