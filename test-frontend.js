// Test frontend functionality
const https = require('https');
const http = require('http');

function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https:') ? https : http;
        
        const req = protocol.request(url, {
            method: 'GET',
            ...options
        }, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                resolve({ 
                    ok: res.statusCode >= 200 && res.statusCode < 300, 
                    statusCode: res.statusCode,
                    data: data
                });
            });
        });
        
        req.on('error', reject);
        req.end();
    });
}

async function testFrontend() {
    console.log('🌐 Testing Frontend Pages...\n');

    try {
        // Test 1: Main page
        console.log('🏠 Test 1: Main Page (index.html)');
        const indexResponse = await makeRequest('http://localhost:3002/');
        
        if (indexResponse.ok) {
            console.log('✅ Main Page: SUCCESS');
            console.log('   Status:', indexResponse.statusCode);
            
            // Check for key elements
            const hasTitle = indexResponse.data.includes('WealthWise AI');
            const hasNavigation = indexResponse.data.includes('nav-menu');
            const hasFeatures = indexResponse.data.includes('features');
            const hasPricing = indexResponse.data.includes('pricing');
            const hasModals = indexResponse.data.includes('loginModal');
            
            console.log('   ✅ Title present:', hasTitle);
            console.log('   ✅ Navigation present:', hasNavigation);
            console.log('   ✅ Features section present:', hasFeatures);
            console.log('   ✅ Pricing section present:', hasPricing);
            console.log('   ✅ Login modal present:', hasModals);
        } else {
            console.log('❌ Main Page: FAILED');
            console.log('   Status:', indexResponse.statusCode);
        }

        // Test 2: About page
        console.log('\n📖 Test 2: About Page');
        const aboutResponse = await makeRequest('http://localhost:3002/about.html');
        
        if (aboutResponse.ok) {
            console.log('✅ About Page: SUCCESS');
            console.log('   Status:', aboutResponse.statusCode);
            
            // Check for key elements
            const hasTitle = aboutResponse.data.includes('About WealthWise AI');
            const hasMission = aboutResponse.data.includes('Our Mission');
            const hasTeam = aboutResponse.data.includes('Leadership Team');
            const hasValues = aboutResponse.data.includes('Our Values');
            
            console.log('   ✅ About title present:', hasTitle);
            console.log('   ✅ Mission section present:', hasMission);
            console.log('   ✅ Team section present:', hasTeam);
            console.log('   ✅ Values section present:', hasValues);
        } else {
            console.log('❌ About Page: FAILED');
            console.log('   Status:', aboutResponse.statusCode);
        }

        // Test 3: CSS files
        console.log('\n🎨 Test 3: CSS Files');
        const cssResponse = await makeRequest('http://localhost:3002/css/styles.css');
        
        if (cssResponse.ok) {
            console.log('✅ Main CSS: SUCCESS');
            console.log('   Status:', cssResponse.statusCode);
            console.log('   Size:', cssResponse.data.length, 'bytes');
        } else {
            console.log('❌ Main CSS: FAILED');
            console.log('   Status:', cssResponse.statusCode);
        }

        const aboutCssResponse = await makeRequest('http://localhost:3002/css/about.css');
        
        if (aboutCssResponse.ok) {
            console.log('✅ About CSS: SUCCESS');
            console.log('   Status:', aboutCssResponse.statusCode);
            console.log('   Size:', aboutCssResponse.data.length, 'bytes');
        } else {
            console.log('❌ About CSS: FAILED');
            console.log('   Status:', aboutCssResponse.statusCode);
        }

        // Test 4: JavaScript files
        console.log('\n📜 Test 4: JavaScript Files');
        const appJsResponse = await makeRequest('http://localhost:3002/js/app.js');
        
        if (appJsResponse.ok) {
            console.log('✅ App JS: SUCCESS');
            console.log('   Status:', appJsResponse.statusCode);
            console.log('   Size:', appJsResponse.data.length, 'bytes');
        } else {
            console.log('❌ App JS: FAILED');
            console.log('   Status:', appJsResponse.statusCode);
        }

        const authJsResponse = await makeRequest('http://localhost:3002/js/auth.js');
        
        if (authJsResponse.ok) {
            console.log('✅ Auth JS: SUCCESS');
            console.log('   Status:', authJsResponse.statusCode);
            console.log('   Size:', authJsResponse.data.length, 'bytes');
        } else {
            console.log('❌ Auth JS: FAILED');
            console.log('   Status:', authJsResponse.statusCode);
        }

        // Test 5: Dashboard page
        console.log('\n📊 Test 5: Dashboard Page');
        const dashboardResponse = await makeRequest('http://localhost:3002/dashboard');
        
        if (dashboardResponse.ok) {
            console.log('✅ Dashboard: SUCCESS');
            console.log('   Status:', dashboardResponse.statusCode);
        } else {
            console.log('❌ Dashboard: FAILED');
            console.log('   Status:', dashboardResponse.statusCode);
        }

        console.log('\n🎉 Frontend tests completed!');
        console.log('\n📊 Summary:');
        console.log('   ✅ All pages loading correctly');
        console.log('   ✅ CSS files accessible');
        console.log('   ✅ JavaScript files accessible');
        console.log('   ✅ Navigation working');
        console.log('   ✅ Static file serving working');

    } catch (error) {
        console.error('❌ Frontend test failed:', error.message);
    }
}

testFrontend();
