const express = require('express');
const jwt = require('jsonwebtoken');

// Use appropriate database based on configuration
const database = process.env.USE_SUPABASE === 'true'
    ? require('../supabase')
    : require('../database');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Middleware to authenticate JWT token
function authenticateToken(req, res, next) {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
        return res.status(401).json({ 
            message: 'Access token required' 
        });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ 
                message: 'Invalid or expired token' 
            });
        }

        req.user = user;
        next();
    });
}

// Get all user accounts
router.get('/accounts', authenticateToken, (req, res) => {
    const userId = req.user.userId;

    database.getUserAccounts(userId, (err, accounts) => {
        if (err) {return res.status(500).json({ 
                message: 'Internal server error' 
            });
        }

        res.json({ accounts });
    });
});

// Add new account
router.post('/accounts', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const { accountType, accountName, institution, balance = 0 } = req.body;

    if (!accountType || !accountName) {
        return res.status(400).json({ 
            message: 'Account type and name are required' 
        });
    }

    const validAccountTypes = ['checking', 'savings', 'investment', 'credit', 'loan', 'other'];
    if (!validAccountTypes.includes(accountType)) {
        return res.status(400).json({ 
            message: 'Invalid account type' 
        });
    }

    database.db.run(
        'INSERT INTO accounts (user_id, account_type, account_name, institution, balance) VALUES (?, ?, ?, ?, ?)',
        [userId, accountType, accountName, institution || '', parseFloat(balance) || 0],
        function(err) {
            if (err) {return res.status(500).json({ 
                    message: 'Failed to create account' 
                });
            }

            res.status(201).json({
                message: 'Account created successfully',
                account: {
                    id: this.lastID,
                    accountType,
                    accountName,
                    institution,
                    balance: parseFloat(balance) || 0
                }
            });
        }
    );
});

// Update account
router.put('/accounts/:accountId', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const accountId = req.params.accountId;
    const { accountName, institution, balance } = req.body;

    if (!accountName) {
        return res.status(400).json({ 
            message: 'Account name is required' 
        });
    }

    database.db.run(
        'UPDATE accounts SET account_name = ?, institution = ?, balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
        [accountName, institution || '', parseFloat(balance) || 0, accountId, userId],
        function(err) {
            if (err) {return res.status(500).json({ 
                    message: 'Failed to update account' 
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({ 
                    message: 'Account not found' 
                });
            }

            res.json({ 
                message: 'Account updated successfully' 
            });
        }
    );
});

// Delete account
router.delete('/accounts/:accountId', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const accountId = req.params.accountId;

    database.db.run(
        'UPDATE accounts SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
        [accountId, userId],
        function(err) {
            if (err) {return res.status(500).json({ 
                    message: 'Failed to delete account' 
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({ 
                    message: 'Account not found' 
                });
            }

            res.json({ 
                message: 'Account deleted successfully' 
            });
        }
    );
});

// Get user transactions
router.get('/transactions', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    database.db.all(
        'SELECT * FROM transactions WHERE user_id = ? ORDER BY transaction_date DESC, created_at DESC LIMIT ? OFFSET ?',
        [userId, limit, offset],
        (err, transactions) => {
            if (err) {return res.status(500).json({ 
                    message: 'Internal server error' 
                });
            }

            res.json({ transactions });
        }
    );
});

// Add new transaction
router.post('/transactions', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const { accountId, amount, description, category, transactionDate } = req.body;

    if (!amount || !description || !transactionDate) {
        return res.status(400).json({ 
            message: 'Amount, description, and transaction date are required' 
        });
    }

    const transactionData = {
        userId,
        accountId: accountId || null,
        amount: parseFloat(amount),
        description,
        category: category || 'Other',
        transactionDate
    };

    database.addTransaction(transactionData, (err, result) => {
        if (err) {return res.status(500).json({ 
                message: 'Failed to add transaction' 
            });
        }

        res.status(201).json({
            message: 'Transaction added successfully',
            transaction: {
                id: result.id,
                ...transactionData
            }
        });
    });
});

// Get user goals
router.get('/goals', authenticateToken, (req, res) => {
    const userId = req.user.userId;

    database.getUserGoals(userId, (err, goals) => {
        if (err) {return res.status(500).json({ 
                message: 'Internal server error' 
            });
        }

        res.json({ goals });
    });
});

// Create new goal
router.post('/goals', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const { name, targetAmount, targetDate, category } = req.body;

    if (!name || !targetAmount) {
        return res.status(400).json({ 
            message: 'Goal name and target amount are required' 
        });
    }

    const goalData = {
        userId,
        name,
        targetAmount: parseFloat(targetAmount),
        targetDate: targetDate || null,
        category: category || 'other'
    };

    database.createGoal(goalData, (err, result) => {
        if (err) {return res.status(500).json({ 
                message: 'Failed to create goal' 
            });
        }

        res.status(201).json({
            message: 'Goal created successfully',
            goal: {
                id: result.id,
                ...goalData,
                currentAmount: 0,
                status: 'active'
            }
        });
    });
});

// Update goal progress
router.put('/goals/:goalId/progress', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const goalId = req.params.goalId;
    const { currentAmount } = req.body;

    if (currentAmount === undefined || currentAmount === null) {
        return res.status(400).json({ 
            message: 'Current amount is required' 
        });
    }

    // First verify the goal belongs to the user
    database.db.get(
        'SELECT * FROM goals WHERE id = ? AND user_id = ?',
        [goalId, userId],
        (err, goal) => {
            if (err) {return res.status(500).json({ 
                    message: 'Internal server error' 
                });
            }

            if (!goal) {
                return res.status(404).json({ 
                    message: 'Goal not found' 
                });
            }

            database.updateGoalProgress(goalId, parseFloat(currentAmount), (err) => {
                if (err) {return res.status(500).json({ 
                        message: 'Failed to update goal progress' 
                    });
                }

                res.json({ 
                    message: 'Goal progress updated successfully' 
                });
            });
        }
    );
});

// Delete goal
router.delete('/goals/:goalId', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const goalId = req.params.goalId;

    database.db.run(
        'DELETE FROM goals WHERE id = ? AND user_id = ?',
        [goalId, userId],
        function(err) {
            if (err) {return res.status(500).json({ 
                    message: 'Failed to delete goal' 
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({ 
                    message: 'Goal not found' 
                });
            }

            res.json({ 
                message: 'Goal deleted successfully' 
            });
        }
    );
});

// Get financial insights
router.get('/insights', authenticateToken, (req, res) => {
    const userId = req.user.userId;

    database.db.all(
        'SELECT * FROM insights WHERE user_id = ? ORDER BY priority DESC, created_at DESC',
        [userId],
        (err, insights) => {
            if (err) {return res.status(500).json({ 
                    message: 'Internal server error' 
                });
            }

            res.json({ insights });
        }
    );
});

// Mark insight as read
router.put('/insights/:insightId/read', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const insightId = req.params.insightId;

    database.db.run(
        'UPDATE insights SET is_read = 1 WHERE id = ? AND user_id = ?',
        [insightId, userId],
        function(err) {
            if (err) {return res.status(500).json({ 
                    message: 'Failed to update insight' 
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({ 
                    message: 'Insight not found' 
                });
            }

            res.json({ 
                message: 'Insight marked as read' 
            });
        }
    );
});

// Get spending analysis
router.get('/analysis/spending', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const period = req.query.period || '30'; // days

    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - parseInt(period));

    database.db.all(
        `SELECT category, SUM(ABS(amount)) as total_amount, COUNT(*) as transaction_count
         FROM transactions 
         WHERE user_id = ? AND amount < 0 AND transaction_date >= ?
         GROUP BY category
         ORDER BY total_amount DESC`,
        [userId, daysAgo.toISOString().split('T')[0]],
        (err, spendingData) => {
            if (err) {return res.status(500).json({ 
                    message: 'Internal server error' 
                });
            }

            res.json({ 
                period: `${period} days`,
                spendingByCategory: spendingData 
            });
        }
    );
});

// Get income analysis
router.get('/analysis/income', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const period = req.query.period || '30'; // days

    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - parseInt(period));

    database.db.all(
        `SELECT category, SUM(amount) as total_amount, COUNT(*) as transaction_count
         FROM transactions 
         WHERE user_id = ? AND amount > 0 AND transaction_date >= ?
         GROUP BY category
         ORDER BY total_amount DESC`,
        [userId, daysAgo.toISOString().split('T')[0]],
        (err, incomeData) => {
            if (err) {return res.status(500).json({ 
                    message: 'Internal server error' 
                });
            }

            res.json({ 
                period: `${period} days`,
                incomeByCategory: incomeData 
            });
        }
    );
});

// Get net worth trend
router.get('/analysis/net-worth', authenticateToken, (req, res) => {
    const userId = req.user.userId;

    // This is a simplified version - in a real app, you'd track historical net worth
    database.getUserAccounts(userId, (err, accounts) => {
        if (err) {return res.status(500).json({ 
                message: 'Internal server error' 
            });
        }

        let totalAssets = 0;
        let totalLiabilities = 0;

        accounts.forEach(account => {
            if (account.balance > 0) {
                totalAssets += account.balance;
            } else {
                totalLiabilities += Math.abs(account.balance);
            }
        });

        const netWorth = totalAssets - totalLiabilities;

        // Generate mock historical data for demonstration
        const mockHistoricalData = [];
        for (let i = 11; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
            const historicalNetWorth = netWorth * (1 + variation);
            
            mockHistoricalData.push({
                date: date.toISOString().split('T')[0],
                netWorth: Math.round(historicalNetWorth * 100) / 100
            });
        }

        res.json({
            currentNetWorth: netWorth,
            totalAssets: totalAssets,
            totalLiabilities: totalLiabilities,
            historicalData: mockHistoricalData
        });
    });
});

module.exports = router;
