const express = require('express');
const jwt = require('jsonwebtoken');

// Use appropriate database based on configuration
const database = process.env.USE_SUPABASE === 'true'
    ? require('../supabase')
    : require('../database');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Middleware to authenticate JWT token
function authenticateToken(req, res, next) {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
        return res.status(401).json({ 
            message: 'Access token required' 
        });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ 
                message: 'Invalid or expired token' 
            });
        }

        req.user = user;
        next();
    });
}

// AI Chat endpoint
router.post('/chat', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { message } = req.body;

        if (!message || message.trim().length === 0) {
            return res.status(400).json({ 
                message: 'Message is required' 
            });
        }

        // Get user's financial context
        const financialContext = await getUserFinancialContext(userId);
        
        // Generate AI response based on message and context
        const aiResponse = await generateAIResponse(message, financialContext);

        // Save chat interaction to database
        database.saveChatMessage(userId, message, aiResponse, (err) => {
            if (err) {
                console.error('Error saving chat message:', err);
                // Don't fail the request if saving fails
            }
        });

        res.json({
            message: aiResponse,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('AI chat error:', error);
        res.status(500).json({ 
            message: 'Sorry, I encountered an error. Please try again.' 
        });
    }
});

// Get chat history
router.get('/chat/history', authenticateToken, (req, res) => {
    const userId = req.user.userId;
    const limit = parseInt(req.query.limit) || 50;

    database.getChatHistory(userId, limit, (err, chatHistory) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ 
                message: 'Internal server error' 
            });
        }

        res.json({ chatHistory });
    });
});

// Generate financial insights
router.post('/insights/generate', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;

        // Get user's financial context
        const financialContext = await getUserFinancialContext(userId);
        
        // Generate insights based on financial data
        const insights = await generateFinancialInsights(financialContext);

        // Save insights to database
        const savedInsights = [];
        for (const insight of insights) {
            await new Promise((resolve, reject) => {
                database.db.run(
                    'INSERT INTO insights (user_id, insight_type, title, description, action_items, priority) VALUES (?, ?, ?, ?, ?, ?)',
                    [userId, insight.type, insight.title, insight.description, JSON.stringify(insight.actionItems), insight.priority],
                    function(err) {
                        if (err) {
                            reject(err);
                        } else {
                            savedInsights.push({
                                id: this.lastID,
                                ...insight
                            });
                            resolve();
                        }
                    }
                );
            });
        }

        res.json({
            message: 'Insights generated successfully',
            insights: savedInsights
        });

    } catch (error) {
        console.error('Error generating insights:', error);
        res.status(500).json({ 
            message: 'Failed to generate insights' 
        });
    }
});

// Scenario planning endpoint
router.post('/scenario/analyze', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { scenario } = req.body;

        if (!scenario || !scenario.type) {
            return res.status(400).json({ 
                message: 'Scenario type is required' 
            });
        }

        // Get user's financial context
        const financialContext = await getUserFinancialContext(userId);
        
        // Analyze scenario
        const analysis = await analyzeScenario(scenario, financialContext);

        res.json({
            scenario: scenario,
            analysis: analysis,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Scenario analysis error:', error);
        res.status(500).json({ 
            message: 'Failed to analyze scenario' 
        });
    }
});

// Helper function to get user's financial context
async function getUserFinancialContext(userId) {
    return new Promise((resolve, reject) => {
        // Get accounts
        database.getUserAccounts(userId, (err, accounts) => {
            if (err) return reject(err);

            // Get recent transactions
            database.getUserTransactions(userId, 100, (err, transactions) => {
                if (err) return reject(err);

                // Get goals
                database.getUserGoals(userId, (err, goals) => {
                    if (err) return reject(err);

                    // Calculate financial metrics
                    let totalAssets = 0;
                    let totalLiabilities = 0;
                    accounts.forEach(account => {
                        if (account.balance > 0) {
                            totalAssets += account.balance;
                        } else {
                            totalLiabilities += Math.abs(account.balance);
                        }
                    });

                    // Calculate monthly income and expenses
                    const currentMonth = new Date().getMonth();
                    const currentYear = new Date().getFullYear();
                    let monthlyIncome = 0;
                    let monthlyExpenses = 0;

                    transactions.forEach(transaction => {
                        const transactionDate = new Date(transaction.transaction_date);
                        if (transactionDate.getMonth() === currentMonth && 
                            transactionDate.getFullYear() === currentYear) {
                            if (transaction.amount > 0) {
                                monthlyIncome += transaction.amount;
                            } else {
                                monthlyExpenses += Math.abs(transaction.amount);
                            }
                        }
                    });

                    const context = {
                        netWorth: totalAssets - totalLiabilities,
                        totalAssets,
                        totalLiabilities,
                        monthlyIncome,
                        monthlyExpenses,
                        savingsRate: monthlyIncome > 0 ? ((monthlyIncome - monthlyExpenses) / monthlyIncome * 100) : 0,
                        accounts,
                        transactions: transactions.slice(0, 20), // Recent transactions
                        goals,
                        emergencyFund: accounts.find(a => a.account_type === 'savings')?.balance || 0
                    };

                    resolve(context);
                });
            });
        });
    });
}

// AI response generation function
async function generateAIResponse(message, context) {
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const lowerMessage = message.toLowerCase();

    // Budget-related responses
    if (lowerMessage.includes('budget') || lowerMessage.includes('spending')) {
        return generateBudgetAdvice(context);
    }

    // Investment-related responses
    if (lowerMessage.includes('invest') || lowerMessage.includes('portfolio') || lowerMessage.includes('stock')) {
        return generateInvestmentAdvice(context);
    }

    // Savings-related responses
    if (lowerMessage.includes('save') || lowerMessage.includes('saving')) {
        return generateSavingsAdvice(context);
    }

    // Retirement-related responses
    if (lowerMessage.includes('retire') || lowerMessage.includes('retirement')) {
        return generateRetirementAdvice(context);
    }

    // Tax-related responses
    if (lowerMessage.includes('tax') || lowerMessage.includes('taxes')) {
        return generateTaxAdvice(context);
    }

    // Goal-related responses
    if (lowerMessage.includes('goal') || lowerMessage.includes('house')) {
        return generateGoalAdvice(context);
    }

    // Emergency fund responses
    if (lowerMessage.includes('emergency')) {
        return generateEmergencyFundAdvice(context);
    }

    // General financial advice
    return generateGeneralAdvice(context);
}

function generateBudgetAdvice(context) {
    const responses = [
        `Great question about budgeting! With your current income of $${context.monthlyIncome.toLocaleString()} and expenses of $${context.monthlyExpenses.toLocaleString()}, you're saving ${context.savingsRate.toFixed(1)}% of your income. Consider the 50/30/20 rule: 50% for needs, 30% for wants, and 20% for savings and debt repayment.`,
        
        `Your current spending pattern shows you're doing well with a ${context.savingsRate.toFixed(1)}% savings rate. To optimize further, try tracking your expenses by category for a month. Based on your transaction history, you could potentially save more on discretionary spending.`,
        
        `Based on your financial profile, you're budgeting effectively. Consider automating your savings to make it even easier. With your current income, you could potentially increase your savings rate if you optimize discretionary spending.`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
}

function generateInvestmentAdvice(context) {
    const investmentAccounts = context.accounts.filter(a => a.account_type === 'investment');
    const totalInvestments = investmentAccounts.reduce((sum, acc) => sum + acc.balance, 0);

    return `With your current investment portfolio of $${totalInvestments.toLocaleString()} and net worth of $${context.netWorth.toLocaleString()}, you're in a good position for long-term growth. Consider a diversified portfolio with age-appropriate asset allocation. Your emergency fund appears solid, so you can take on appropriate investment risk for long-term goals.`;
}

function generateSavingsAdvice(context) {
    return `You're saving ${context.savingsRate.toFixed(1)}% of your income - that's ${context.savingsRate > 20 ? 'excellent' : context.savingsRate > 10 ? 'good' : 'a good start'}! The average American saves only 13%. With your emergency fund at $${context.emergencyFund.toLocaleString()}, ${context.emergencyFund > context.monthlyExpenses * 3 ? 'focus on investing additional savings for long-term growth' : 'consider building it up to 3-6 months of expenses first'}.`;
}

function generateRetirementAdvice(context) {
    const retirementGoal = context.goals.find(g => g.name.toLowerCase().includes('retirement'));
    if (retirementGoal) {
        const monthsToTarget = retirementGoal.target_date ? 
            Math.max(0, (new Date(retirementGoal.target_date) - new Date()) / (1000 * 60 * 60 * 24 * 30)) : 360;
        const monthlyNeeded = (retirementGoal.target_amount - retirementGoal.current_amount) / monthsToTarget;
        
        return `You have $${retirementGoal.current_amount.toLocaleString()} saved for retirement toward your $${retirementGoal.target_amount.toLocaleString()} goal. To reach this target, you'd need about $${monthlyNeeded.toLocaleString()}/month in contributions, assuming 7% annual returns. Consider maximizing your 401(k) match and IRA contributions for tax advantages.`;
    }
    
    return `Retirement planning is crucial! Based on your current financial situation, consider setting up automatic contributions to tax-advantaged accounts like 401(k) and IRA. A general rule is to save 10-15% of your income for retirement.`;
}

function generateTaxAdvice(context) {
    return `With your income level, consider maximizing tax-advantaged accounts. You can contribute up to $22,500 to your 401(k) and $6,500 to an IRA annually (2023 limits). This could save you significant taxes while building wealth. Also consider HSA contributions if available - it's triple tax-advantaged.`;
}

function generateGoalAdvice(context) {
    if (context.goals.length === 0) {
        return `Setting financial goals is a great way to stay motivated! Consider starting with an emergency fund (3-6 months of expenses), then move on to specific goals like a house down payment, vacation, or retirement. I can help you create a plan to reach these goals.`;
    }
    
    const activeGoals = context.goals.filter(g => g.status === 'active');
    return `You have ${activeGoals.length} active financial goals - that's great planning! Based on your current savings rate of ${context.savingsRate.toFixed(1)}%, you're making good progress. Would you like me to analyze the timeline for any specific goal?`;
}

function generateEmergencyFundAdvice(context) {
    const monthsCovered = context.emergencyFund / context.monthlyExpenses;
    
    if (monthsCovered >= 6) {
        return `Excellent work on your emergency fund! You have $${context.emergencyFund.toLocaleString()} saved, which covers ${monthsCovered.toFixed(1)} months of expenses. This exceeds the recommended 3-6 months. Since you're well-covered, consider investing additional emergency savings for better returns.`;
    } else if (monthsCovered >= 3) {
        return `Good job on your emergency fund! You have ${monthsCovered.toFixed(1)} months of expenses saved. Consider building it up to 6 months for extra security, especially if you have variable income or job uncertainty.`;
    } else {
        return `Your emergency fund could use some attention. With $${context.emergencyFund.toLocaleString()} saved, you have ${monthsCovered.toFixed(1)} months of expenses covered. Aim for 3-6 months of expenses ($${(context.monthlyExpenses * 3).toLocaleString()} - $${(context.monthlyExpenses * 6).toLocaleString()}) in a high-yield savings account.`;
    }
}

function generateGeneralAdvice(context) {
    const responses = [
        `Your financial health looks ${context.savingsRate > 15 ? 'strong' : 'decent'}! With a ${context.savingsRate.toFixed(1)}% savings rate and net worth of $${context.netWorth.toLocaleString()}, you're ${context.netWorth > 0 ? 'building wealth effectively' : 'working toward positive net worth'}. Keep focusing on your goals and consider optimizing your investment strategy.`,
        
        `You're making progress with your finances. Your net worth of $${context.netWorth.toLocaleString()} shows ${context.netWorth > 0 ? 'positive momentum' : 'room for improvement'}. Consider these next steps: optimize your budget, maximize tax-advantaged accounts, and ensure you have adequate insurance coverage.`,
        
        `Based on your financial profile, you have ${context.netWorth > 50000 ? 'a solid foundation' : 'a good starting point'}. Focus on: 1) Consistent saving and investing, 2) Optimizing your asset allocation, 3) Minimizing fees and taxes, 4) Regular financial check-ups to stay on track.`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
}

// Generate financial insights
async function generateFinancialInsights(context) {
    const insights = [];

    // Emergency fund insight
    const monthsCovered = context.emergencyFund / context.monthlyExpenses;
    if (monthsCovered < 3) {
        insights.push({
            type: 'emergency_fund',
            title: 'Build Your Emergency Fund',
            description: `Your emergency fund covers only ${monthsCovered.toFixed(1)} months of expenses. Aim for 3-6 months.`,
            actionItems: ['Set up automatic transfers to savings', 'Consider a high-yield savings account'],
            priority: 3
        });
    }

    // Savings rate insight
    if (context.savingsRate < 20) {
        insights.push({
            type: 'savings_rate',
            title: 'Increase Your Savings Rate',
            description: `Your current savings rate is ${context.savingsRate.toFixed(1)}%. Consider increasing it to 20% or more.`,
            actionItems: ['Review and reduce discretionary spending', 'Automate savings transfers'],
            priority: 2
        });
    }

    // Investment allocation insight
    const investmentBalance = context.accounts
        .filter(a => a.account_type === 'investment')
        .reduce((sum, acc) => sum + acc.balance, 0);
    
    if (investmentBalance < context.netWorth * 0.6 && context.netWorth > 10000) {
        insights.push({
            type: 'investment_allocation',
            title: 'Optimize Investment Allocation',
            description: 'Consider increasing your investment allocation for long-term growth.',
            actionItems: ['Review asset allocation', 'Consider low-cost index funds'],
            priority: 2
        });
    }

    return insights;
}

// Scenario analysis function
async function analyzeScenario(scenario, context) {
    const { type, parameters } = scenario;

    switch (type) {
        case 'job_loss':
            return analyzeJobLossScenario(parameters, context);
        case 'salary_increase':
            return analyzeSalaryIncreaseScenario(parameters, context);
        case 'major_purchase':
            return analyzeMajorPurchaseScenario(parameters, context);
        case 'market_downturn':
            return analyzeMarketDownturnScenario(parameters, context);
        default:
            return { error: 'Unknown scenario type' };
    }
}

function analyzeJobLossScenario(parameters, context) {
    const monthsWithoutIncome = parameters.months || 6;
    const emergencyFundMonths = context.emergencyFund / context.monthlyExpenses;
    
    return {
        impact: emergencyFundMonths >= monthsWithoutIncome ? 'manageable' : 'challenging',
        emergencyFundCoverage: `${emergencyFundMonths.toFixed(1)} months`,
        recommendations: emergencyFundMonths < monthsWithoutIncome ? 
            ['Build larger emergency fund', 'Consider reducing expenses', 'Explore unemployment benefits'] :
            ['Your emergency fund should cover this scenario', 'Consider temporary expense reduction']
    };
}

function analyzeSalaryIncreaseScenario(parameters, context) {
    const increaseAmount = parameters.amount || context.monthlyIncome * 0.1;
    const newSavingsRate = ((context.monthlyIncome + increaseAmount - context.monthlyExpenses) / (context.monthlyIncome + increaseAmount)) * 100;
    
    return {
        impact: 'positive',
        newMonthlySavings: (context.monthlyIncome + increaseAmount - context.monthlyExpenses).toFixed(2),
        newSavingsRate: `${newSavingsRate.toFixed(1)}%`,
        recommendations: [
            'Increase retirement contributions',
            'Boost emergency fund if needed',
            'Consider additional goal funding'
        ]
    };
}

function analyzeMajorPurchaseScenario(parameters, context) {
    const purchaseAmount = parameters.amount || 50000;
    const newNetWorth = context.netWorth - purchaseAmount;
    const impactOnEmergencyFund = Math.max(0, context.emergencyFund - purchaseAmount);
    
    return {
        impact: newNetWorth > 0 ? 'manageable' : 'significant',
        newNetWorth: newNetWorth.toFixed(2),
        emergencyFundImpact: impactOnEmergencyFund.toFixed(2),
        recommendations: newNetWorth < 0 ? 
            ['Consider financing options', 'Delay purchase to save more', 'Look for lower-cost alternatives'] :
            ['Ensure emergency fund remains intact', 'Consider impact on other goals']
    };
}

function analyzeMarketDownturnScenario(parameters, context) {
    const downturnPercent = parameters.percent || 20;
    const investmentAccounts = context.accounts.filter(a => a.account_type === 'investment');
    const totalInvestments = investmentAccounts.reduce((sum, acc) => sum + acc.balance, 0);
    const potentialLoss = totalInvestments * (downturnPercent / 100);
    const newNetWorth = context.netWorth - potentialLoss;
    
    return {
        impact: 'temporary',
        potentialLoss: potentialLoss.toFixed(2),
        newNetWorth: newNetWorth.toFixed(2),
        recommendations: [
            'Stay the course - markets recover over time',
            'Consider it a buying opportunity',
            'Rebalance if needed',
            'Avoid emotional decisions'
        ]
    };
}

module.exports = router;
