const express = require('express');
const jwt = require('jsonwebtoken');
const aiService = require('../ai-service');

// Use appropriate database based on configuration
const database = process.env.USE_SUPABASE === 'true' 
    ? require('../supabase') 
    : require('../database');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Middleware to authenticate JWT token
function authenticateToken(req, res, next) {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
        return res.status(401).json({ 
            message: 'Access token required' 
        });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ 
                message: 'Invalid or expired token' 
            });
        }

        req.user = user;
        next();
    });
}

// AI Chat endpoint
router.post('/chat', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { message } = req.body;

        if (!message || message.trim().length === 0) {
            return res.status(400).json({ 
                message: 'Message is required' 
            });
        }

        // Get user's financial context
        const financialContext = await getUserFinancialContext(userId);
        
        // Generate AI response using Claude 3.5 Sonnet
        const aiResponse = await aiService.generateResponse(message, financialContext);

        // Save chat interaction to database
        try {
            await database.saveChatMessage(userId, message, aiResponse);
        } catch (err) {
            console.error('Error saving chat message:', err);
            // Don't fail the request if saving fails
        }

        res.json({
            message: aiResponse,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('AI chat error:', error);
        res.status(500).json({ 
            message: 'Sorry, I encountered an error. Please try again.' 
        });
    }
});

// Get chat history
router.get('/chat/history', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const limit = parseInt(req.query.limit) || 50;

        const chatHistory = await database.getChatHistory(userId, limit);

        res.json({ chatHistory });
    } catch (error) {
        console.error('Database error:', error);
        res.status(500).json({ 
            message: 'Internal server error' 
        });
    }
});

// Generate financial insights
router.post('/insights/generate', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;

        // Get user's financial context
        const financialContext = await getUserFinancialContext(userId);
        
        // Generate insights using Claude 3.5 Sonnet
        const insightsResponse = await aiService.generateFinancialInsights(financialContext);
        const insights = insightsResponse.insights || [];

        // Save insights to database
        const savedInsights = [];
        for (const insight of insights) {
            try {
                const savedInsight = await database.createInsight({
                    user_id: userId,
                    insight_type: insight.type,
                    title: insight.title,
                    description: insight.description,
                    action_items: insight.actionItems,
                    priority: insight.priority
                });
                savedInsights.push(savedInsight);
            } catch (err) {
                console.error('Error saving insight:', err);
            }
        }

        res.json({
            message: 'Insights generated successfully',
            insights: savedInsights
        });

    } catch (error) {
        console.error('Error generating insights:', error);
        res.status(500).json({ 
            message: 'Failed to generate insights' 
        });
    }
});

// Scenario planning endpoint
router.post('/scenario/analyze', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { scenario } = req.body;

        if (!scenario || !scenario.type) {
            return res.status(400).json({ 
                message: 'Scenario type is required' 
            });
        }

        // Get user's financial context
        const financialContext = await getUserFinancialContext(userId);
        
        // Analyze scenario using Claude 3.5 Sonnet
        const analysis = await aiService.analyzeScenario(scenario, financialContext);

        res.json({
            scenario: scenario,
            analysis: analysis,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('Scenario analysis error:', error);
        res.status(500).json({ 
            message: 'Failed to analyze scenario' 
        });
    }
});

// Helper function to get user's financial context
async function getUserFinancialContext(userId) {
    try {
        // Get accounts, transactions, and goals
        const accounts = await database.getUserAccounts(userId);
        const transactions = await database.getUserTransactions(userId, 100);
        const goals = await database.getUserGoals(userId);

        // Calculate financial metrics
        let totalAssets = 0;
        let totalLiabilities = 0;
        let totalInvestments = 0;
        
        accounts.forEach(account => {
            const balance = parseFloat(account.balance) || 0;
            if (balance > 0) {
                totalAssets += balance;
                if (account.account_type === 'investment') {
                    totalInvestments += balance;
                }
            } else {
                totalLiabilities += Math.abs(balance);
            }
        });

        // Calculate monthly income and expenses
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        let monthlyIncome = 0;
        let monthlyExpenses = 0;

        transactions.forEach(transaction => {
            const transactionDate = new Date(transaction.transaction_date);
            const amount = parseFloat(transaction.amount) || 0;
            
            if (transactionDate.getMonth() === currentMonth && 
                transactionDate.getFullYear() === currentYear) {
                if (amount > 0) {
                    monthlyIncome += amount;
                } else {
                    monthlyExpenses += Math.abs(amount);
                }
            }
        });

        const emergencyFund = accounts.find(a => a.account_type === 'savings')?.balance || 0;
        const savingsRate = monthlyIncome > 0 ? ((monthlyIncome - monthlyExpenses) / monthlyIncome * 100) : 0;

        return {
            netWorth: totalAssets - totalLiabilities,
            totalAssets,
            totalLiabilities,
            totalInvestments,
            monthlyIncome,
            monthlyExpenses,
            savingsRate,
            accounts,
            transactions: transactions.slice(0, 20), // Recent transactions
            goals,
            emergencyFund: parseFloat(emergencyFund) || 0
        };
    } catch (error) {
        console.error('Error getting financial context:', error);
        throw error;
    }
}

module.exports = router;
