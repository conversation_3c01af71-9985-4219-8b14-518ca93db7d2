// Test login API directly
const http = require('http');

function testLogin() {
    console.log('🔐 Testing Login API...\n');

    const loginData = JSON.stringify({
        email: '<EMAIL>',
        password: 'demo123'
    });

    const options = {
        hostname: 'localhost',
        port: 3002,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log('Response Status:', res.statusCode);
            console.log('Response Headers:', res.headers);
            
            try {
                const jsonData = JSON.parse(data);
                
                if (res.statusCode === 200) {
                    console.log('✅ LOGIN SUCCESS!');
                    console.log('User:', jsonData.user.name);
                    console.log('Email:', jsonData.user.email);
                    console.log('Plan:', jsonData.user.planType);
                    console.log('Token generated:', jsonData.token ? 'YES' : 'NO');
                } else {
                    console.log('❌ LOGIN FAILED');
                    console.log('Error:', jsonData.message);
                }
            } catch (error) {
                console.log('❌ INVALID JSON RESPONSE');
                console.log('Raw response:', data);
            }
        });
    });

    req.on('error', (error) => {
        console.log('❌ CONNECTION ERROR');
        console.log('Error:', error.message);
        console.log('\n🔧 This means:');
        console.log('   - Server is not running on port 3002');
        console.log('   - Network connectivity issues');
        console.log('   - Firewall blocking the connection');
    });

    req.write(loginData);
    req.end();
}

// Also test health endpoint
function testHealth() {
    console.log('🏥 Testing Health API...\n');

    const options = {
        hostname: 'localhost',
        port: 3002,
        path: '/api/health',
        method: 'GET'
    };

    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            console.log('Health Status:', res.statusCode);
            
            try {
                const jsonData = JSON.parse(data);
                
                if (res.statusCode === 200) {
                    console.log('✅ SERVER IS RUNNING!');
                    console.log('Status:', jsonData.status);
                    console.log('Version:', jsonData.version);
                    console.log('Timestamp:', jsonData.timestamp);
                    
                    // Now test login
                    setTimeout(testLogin, 1000);
                } else {
                    console.log('❌ SERVER ERROR');
                    console.log('Response:', jsonData);
                }
            } catch (error) {
                console.log('❌ INVALID JSON RESPONSE');
                console.log('Raw response:', data);
            }
        });
    });

    req.on('error', (error) => {
        console.log('❌ SERVER NOT RUNNING');
        console.log('Error:', error.message);
        console.log('\n🔧 To fix this:');
        console.log('   1. Run: node server/server.js');
        console.log('   2. Make sure port 3002 is available');
        console.log('   3. Check for any startup errors');
    });

    req.end();
}

// Start the test
testHealth();
