// Test Gemini AI integration
require('dotenv').config();
const aiService = require('./server/ai-service');

async function testGeminiAI() {
    console.log('🤖 Testing Gemini 2.0 Flash Integration...\n');

    // Mock financial context
    const mockContext = {
        netWorth: 247850,
        totalAssets: 250350,
        totalLiabilities: 2500,
        totalInvestments: 150000,
        monthlyIncome: 8500,
        monthlyExpenses: 4250,
        savingsRate: 50,
        emergencyFund: 25000,
        accounts: [
            { account_type: 'checking', balance: 15000 },
            { account_type: 'savings', balance: 25000 },
            { account_type: 'investment', balance: 150000 },
            { account_type: 'credit', balance: -2500 }
        ],
        goals: [
            { name: 'Emergency Fund', target_amount: 25000, current_amount: 25000 },
            { name: 'House Down Payment', target_amount: 100000, current_amount: 65000 },
            { name: 'Retirement', target_amount: 1000000, current_amount: 350000 }
        ]
    };

    try {
        // Test 1: General financial advice
        console.log('📊 Test 1: General Financial Advice');
        console.log('Question: "How am I doing financially?"');
        const response1 = await aiService.generateResponse(
            "How am I doing financially? What should I focus on next?", 
            mockContext
        );
        console.log('Gemini Response:', response1);
        console.log('\n' + '='.repeat(80) + '\n');

        // Test 2: Investment advice
        console.log('💰 Test 2: Investment Advice');
        console.log('Question: "Should I invest more in stocks?"');
        const response2 = await aiService.generateResponse(
            "Should I invest more in stocks? What's the right allocation for me?", 
            mockContext
        );
        console.log('Gemini Response:', response2);
        console.log('\n' + '='.repeat(80) + '\n');

        // Test 3: Budget optimization
        console.log('📈 Test 3: Budget Optimization');
        console.log('Question: "How can I optimize my budget?"');
        const response3 = await aiService.generateResponse(
            "How can I optimize my budget to save more money?", 
            mockContext
        );
        console.log('Gemini Response:', response3);
        console.log('\n' + '='.repeat(80) + '\n');

        // Test 4: Generate financial insights
        console.log('💡 Test 4: Financial Insights Generation');
        const insights = await aiService.generateFinancialInsights(mockContext);
        console.log('Generated Insights:');
        console.log(JSON.stringify(insights, null, 2));
        console.log('\n' + '='.repeat(80) + '\n');

        // Test 5: Scenario analysis
        console.log('🎯 Test 5: Scenario Analysis');
        const scenario = {
            type: 'job_loss',
            name: 'Job Loss Scenario',
            parameters: { months: 6 }
        };
        const analysis = await aiService.analyzeScenario(scenario, mockContext);
        console.log('Scenario Analysis:');
        console.log(JSON.stringify(analysis, null, 2));

        console.log('\n✅ All Gemini AI tests completed successfully!');

    } catch (error) {
        console.error('❌ Error testing Gemini AI:', error);
        
        // Test fallback responses
        console.log('\n🔄 Testing fallback responses...');
        try {
            const fallbackResponse = aiService.generateFallbackResponse(
                "How am I doing financially?", 
                mockContext
            );
            console.log('Fallback Response:', fallbackResponse);
            console.log('✅ Fallback system working correctly');
        } catch (fallbackError) {
            console.error('❌ Fallback system also failed:', fallbackError);
        }
    }
}

// Run the test
testGeminiAI();
