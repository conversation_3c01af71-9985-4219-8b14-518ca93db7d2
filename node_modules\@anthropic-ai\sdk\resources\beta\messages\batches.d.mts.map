{"version": 3, "file": "batches.d.mts", "sourceRoot": "", "sources": ["../../../src/resources/beta/messages/batches.ts"], "names": [], "mappings": "OAEO,EAAE,WAAW,EAAE;OACf,KAAK,OAAO;OACZ,EAAE,UAAU,EAAE;OACd,KAAK,eAAe;OACpB,EAAE,IAAI,EAAE,KAAK,UAAU,EAAE,WAAW,EAAE;OAEtC,EAAE,cAAc,EAAE;OAClB,EAAE,YAAY,EAAE;AAIvB,qBAAa,OAAQ,SAAQ,WAAW;IACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,MAAM,CAAC,MAAM,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,gBAAgB,CAAC;IAYzF;;;;;;;;;;;;;;;OAeG;IACH,QAAQ,CACN,cAAc,EAAE,MAAM,EACtB,MAAM,GAAE,mBAAmB,GAAG,IAAI,GAAG,SAAc,EACnD,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,gBAAgB,CAAC;IAW/B;;;;;;;;;;;;;;OAcG;IACH,IAAI,CACF,MAAM,GAAE,eAAe,GAAG,IAAI,GAAG,SAAc,EAC/C,OAAO,CAAC,EAAE,cAAc,GACvB,WAAW,CAAC,sBAAsB,EAAE,gBAAgB,CAAC;IAYxD;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CACJ,cAAc,EAAE,MAAM,EACtB,MAAM,GAAE,iBAAiB,GAAG,IAAI,GAAG,SAAc,EACjD,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,uBAAuB,CAAC;IAWtC;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,MAAM,CACJ,cAAc,EAAE,MAAM,EACtB,MAAM,GAAE,iBAAiB,GAAG,IAAI,GAAG,SAAc,EACjD,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,gBAAgB,CAAC;IAW/B;;;;;;;;;;;;;;;;;OAiBG;IACG,OAAO,CACX,cAAc,EAAE,MAAM,EACtB,MAAM,GAAE,kBAAkB,GAAG,SAAc,EAC3C,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,YAAY,CAAC,kCAAkC,CAAC,CAAC;CA0B7D;AAED,MAAM,MAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAE5D,MAAM,WAAW,uBAAuB;IACtC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;;OAIG;IACH,IAAI,EAAE,uBAAuB,CAAC;CAC/B;AAED,MAAM,WAAW,gBAAgB;IAC/B;;;;OAIG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;OAGG;IACH,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B;;;OAGG;IACH,mBAAmB,EAAE,MAAM,GAAG,IAAI,CAAC;IAEnC;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;;;OAMG;IACH,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAExB;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,iBAAiB,EAAE,aAAa,GAAG,WAAW,GAAG,OAAO,CAAC;IAEzD;;;;;;OAMG;IACH,cAAc,EAAE,6BAA6B,CAAC;IAE9C;;;;;;OAMG;IACH,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3B;;;;OAIG;IACH,IAAI,EAAE,eAAe,CAAC;CACvB;AAED,MAAM,WAAW,8BAA8B;IAC7C,IAAI,EAAE,UAAU,CAAC;CAClB;AAED,MAAM,WAAW,6BAA6B;IAC5C,KAAK,EAAE,OAAO,CAAC,iBAAiB,CAAC;IAEjC,IAAI,EAAE,SAAS,CAAC;CACjB;AAED,MAAM,WAAW,6BAA6B;IAC5C,IAAI,EAAE,SAAS,CAAC;CACjB;AAED;;;GAGG;AACH,MAAM,WAAW,kCAAkC;IACjD;;;;;OAKG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;;;;;OAMG;IACH,MAAM,EAAE,sBAAsB,CAAC;CAChC;AAED,MAAM,WAAW,6BAA6B;IAC5C;;;;OAIG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;;;OAIG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;;OAIG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;OAIG;IACH,SAAS,EAAE,MAAM,CAAC;CACnB;AAED;;;;;;GAMG;AACH,MAAM,MAAM,sBAAsB,GAC9B,+BAA+B,GAC/B,6BAA6B,GAC7B,8BAA8B,GAC9B,6BAA6B,CAAC;AAElC,MAAM,WAAW,+BAA+B;IAC9C,OAAO,EAAE,eAAe,CAAC,WAAW,CAAC;IAErC,IAAI,EAAE,WAAW,CAAC;CACnB;AAED,MAAM,WAAW,iBAAiB;IAChC;;;OAGG;IACH,QAAQ,EAAE,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAE3C;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,yBAAiB,iBAAiB,CAAC;IACjC,UAAiB,OAAO;QACtB;;;;;WAKG;QACH,SAAS,EAAE,MAAM,CAAC;QAElB;;;;;WAKG;QACH,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,+BAA+B,EAAE,OAAO,CAAC,CAAC;KACxE;CACF;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,eAAgB,SAAQ,UAAU;IACjD;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;CACtC;AAED,MAAM,CAAC,OAAO,WAAW,OAAO,CAAC;IAC/B,OAAO,EACL,KAAK,uBAAuB,IAAI,uBAAuB,EACvD,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,8BAA8B,IAAI,8BAA8B,EACrE,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,kCAAkC,IAAI,kCAAkC,EAC7E,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,+BAA+B,IAAI,+BAA+B,EACvE,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,eAAe,IAAI,eAAe,EACvC,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,kBAAkB,IAAI,kBAAkB,GAC9C,CAAC;CACH"}