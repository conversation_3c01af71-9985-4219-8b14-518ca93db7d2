<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login - WealthWise AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
    </style>
</head>
<body>
    <h1>Test Login</h1>
    
    <form id="testLoginForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="demo123" required>
        </div>
        
        <button type="submit" id="loginBtn">Login</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('testLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const result = document.getElementById('result');
            
            // Show loading state
            loginBtn.textContent = 'Logging in...';
            loginBtn.disabled = true;
            result.innerHTML = '';
            
            try {
                console.log('Attempting login with:', { email, password });
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                console.log('Response status:', response.status);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <h3>✅ Login Successful!</h3>
                        <p><strong>User:</strong> ${data.user.name}</p>
                        <p><strong>Email:</strong> ${data.user.email}</p>
                        <p><strong>Plan:</strong> ${data.user.planType}</p>
                        <p><strong>Token:</strong> ${data.token.substring(0, 50)}...</p>
                        <button onclick="goToDashboard('${data.token}')">Go to Dashboard</button>
                    `;
                } else {
                    throw new Error(data.message || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                result.className = 'result error';
                result.innerHTML = `
                    <h3>❌ Login Failed</h3>
                    <p>${error.message}</p>
                `;
            } finally {
                loginBtn.textContent = 'Login';
                loginBtn.disabled = false;
            }
        });
        
        function goToDashboard(token) {
            // Store token and redirect
            localStorage.setItem('authToken', token);
            window.location.href = '/dashboard.html';
        }
        
        // Test API health on page load
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                console.log('API Health:', data);
            } catch (error) {
                console.error('API Health check failed:', error);
            }
        });
    </script>
</body>
</html>
