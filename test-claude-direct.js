// Direct test of Claude API
require('dotenv').config();
const Anthropic = require('@anthropic-ai/sdk');

async function testClaudeDirect() {
    console.log('🧪 Testing Claude API directly...');
    console.log('API Key:', process.env.CLAUDE_API_KEY ? 'Present' : 'Missing');
    console.log('API Key prefix:', process.env.CLAUDE_API_KEY?.substring(0, 15) + '...');

    try {
        const anthropic = new Anthropic({
            apiKey: process.env.CLAUDE_API_KEY,
        });

        console.log('✅ Anthropic client created successfully');

        const response = await anthropic.messages.create({
            model: 'claude-3-5-sonnet-20241022',
            max_tokens: 100,
            messages: [
                {
                    role: 'user',
                    content: 'Hello! Can you help me with financial advice?'
                }
            ]
        });

        console.log('✅ Claude API call successful!');
        console.log('Response:', response.content[0].text);

    } catch (error) {
        console.error('❌ Claude API error:', error.message);
        console.error('Error details:', error);
        
        // Try alternative initialization
        console.log('\n🔄 Trying alternative initialization...');
        try {
            const anthropic2 = new Anthropic();
            anthropic2.apiKey = process.env.CLAUDE_API_KEY;
            
            const response2 = await anthropic2.messages.create({
                model: 'claude-3-5-sonnet-20241022',
                max_tokens: 50,
                messages: [
                    {
                        role: 'user',
                        content: 'Test message'
                    }
                ]
            });
            
            console.log('✅ Alternative method worked!');
            console.log('Response:', response2.content[0].text);
        } catch (error2) {
            console.error('❌ Alternative method also failed:', error2.message);
        }
    }
}

testClaudeDirect();
