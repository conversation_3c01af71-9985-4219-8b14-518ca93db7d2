// <PERSON>ript to get Supabase API keys
// This is a temporary script to help get the API keys from Supabase dashboard

console.log('🔑 Supabase API Configuration');
console.log('============================');
console.log('');
console.log('Project ID: hkvhlqyyxjwnhzldxnao');
console.log('Project URL: https://hkvhlqyyxjwnhzldxnao.supabase.co');
console.log('');
console.log('To get your API keys:');
console.log('1. Go to https://supabase.com/dashboard/project/hkvhlqyyxjwnhzldxnao/settings/api');
console.log('2. Copy the "anon public" key');
console.log('3. Copy the "service_role" key (keep this secret!)');
console.log('4. Update the .env file with these keys');
console.log('');
console.log('For now, the application will use placeholder keys.');
console.log('The database tables are already created and populated with sample data.');
console.log('');
console.log('Sample user credentials:');
console.log('Email: <EMAIL>');
console.log('Password: demo123');
console.log('');

// For development, we'll use placeholder keys
// In production, these should be real keys from Supabase dashboard
const placeholderKeys = {
    url: 'https://hkvhlqyyxjwnhzldxnao.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhrdmhscXl5eGp3bmh6bGR4bmFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMwOTc4MjksImV4cCI6MjA0ODY3MzgyOX0.placeholder-anon-key',
    serviceKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhrdmhscXl5eGp3bmh6bGR4bmFvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzA5NzgyOSwiZXhwIjoyMDQ4NjczODI5fQ.placeholder-service-key'
};

console.log('Placeholder configuration:');
console.log('SUPABASE_URL=' + placeholderKeys.url);
console.log('SUPABASE_ANON_KEY=' + placeholderKeys.anonKey);
console.log('SUPABASE_SERVICE_ROLE_KEY=' + placeholderKeys.serviceKey);
console.log('');
console.log('⚠️  Remember to replace these with real keys from your Supabase dashboard!');
