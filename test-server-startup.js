// Test server startup and basic functionality
const http = require('http');

function testServer() {
    console.log('🧪 Testing WealthWise AI Server...\n');

    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    const healthReq = http.request({
        hostname: 'localhost',
        port: 3002,
        path: '/api/health',
        method: 'GET'
    }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
            if (res.statusCode === 200) {
                console.log('✅ Health check: SUCCESS');
                console.log('   Response:', JSON.parse(data));
                testSignup();
            } else {
                console.log('❌ Health check: FAILED');
                console.log('   Status:', res.statusCode);
                console.log('   Response:', data);
            }
        });
    });

    healthReq.on('error', (err) => {
        console.log('❌ Health check: ERROR');
        console.log('   Error:', err.message);
        console.log('\n🔧 Possible issues:');
        console.log('   - Server not running on port 3002');
        console.log('   - Database connection issues');
        console.log('   - Missing dependencies');
        console.log('\n💡 Try running: node server/server.js');
    });

    healthReq.end();
}

function testSignup() {
    console.log('\n2. Testing signup endpoint...');
    
    const signupData = JSON.stringify({
        name: 'Test User',
        email: 'test' + Date.now() + '@example.com',
        password: 'testpass123'
    });

    const signupReq = http.request({
        hostname: 'localhost',
        port: 3002,
        path: '/api/auth/signup',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(signupData)
        }
    }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
            if (res.statusCode === 201) {
                console.log('✅ Signup test: SUCCESS');
                const response = JSON.parse(data);
                console.log('   User created:', response.user.name);
                console.log('   Token generated:', response.token ? 'YES' : 'NO');
                testLogin();
            } else {
                console.log('❌ Signup test: FAILED');
                console.log('   Status:', res.statusCode);
                console.log('   Response:', data);
                testLogin(); // Still try login with demo account
            }
        });
    });

    signupReq.on('error', (err) => {
        console.log('❌ Signup test: ERROR');
        console.log('   Error:', err.message);
        testLogin(); // Still try login
    });

    signupReq.write(signupData);
    signupReq.end();
}

function testLogin() {
    console.log('\n3. Testing login endpoint...');
    
    const loginData = JSON.stringify({
        email: '<EMAIL>',
        password: 'demo123'
    });

    const loginReq = http.request({
        hostname: 'localhost',
        port: 3002,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
            if (res.statusCode === 200) {
                console.log('✅ Login test: SUCCESS');
                const response = JSON.parse(data);
                console.log('   User:', response.user.name);
                console.log('   Plan:', response.user.planType);
                testContactSales();
            } else {
                console.log('❌ Login test: FAILED');
                console.log('   Status:', res.statusCode);
                console.log('   Response:', data);
                testContactSales(); // Still try contact sales
            }
        });
    });

    loginReq.on('error', (err) => {
        console.log('❌ Login test: ERROR');
        console.log('   Error:', err.message);
        testContactSales(); // Still try contact sales
    });

    loginReq.write(loginData);
    loginReq.end();
}

function testContactSales() {
    console.log('\n4. Testing contact sales endpoint...');
    
    const contactData = JSON.stringify({
        name: 'Test Contact',
        email: '<EMAIL>',
        company: 'Test Company',
        message: 'Test message',
        consent: true
    });

    const contactReq = http.request({
        hostname: 'localhost',
        port: 3002,
        path: '/api/contact/sales',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(contactData)
        }
    }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
            if (res.statusCode === 200) {
                console.log('✅ Contact sales test: SUCCESS');
                const response = JSON.parse(data);
                console.log('   Message:', response.message);
                showSummary();
            } else {
                console.log('❌ Contact sales test: FAILED');
                console.log('   Status:', res.statusCode);
                console.log('   Response:', data);
                showSummary();
            }
        });
    });

    contactReq.on('error', (err) => {
        console.log('❌ Contact sales test: ERROR');
        console.log('   Error:', err.message);
        showSummary();
    });

    contactReq.write(contactData);
    contactReq.end();
}

function showSummary() {
    console.log('\n' + '='.repeat(50));
    console.log('🎯 SERVER TEST SUMMARY');
    console.log('='.repeat(50));
    console.log('✅ If all tests passed, the server is working correctly');
    console.log('❌ If tests failed, check the error messages above');
    console.log('');
    console.log('🔧 Common fixes for network errors:');
    console.log('   1. Make sure server is running: node server/server.js');
    console.log('   2. Check port 3002 is not blocked');
    console.log('   3. Verify all dependencies installed: npm install');
    console.log('   4. Check database permissions');
    console.log('');
    console.log('🌐 Test URLs:');
    console.log('   - Health: http://localhost:3002/api/health');
    console.log('   - Main App: http://localhost:3002/');
    console.log('   - Contact: http://localhost:3002/contact.html');
}

// Start the test
testServer();
