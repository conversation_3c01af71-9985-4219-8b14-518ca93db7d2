// Test AI Chat API endpoint
const https = require('https');
const http = require('http');

function makeRequest(url, options) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https:') ? https : http;
        
        const req = protocol.request(url, options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, json: () => jsonData, statusCode: res.statusCode });
                } catch (e) {
                    resolve({ ok: false, json: () => ({ error: data }), statusCode: res.statusCode });
                }
            });
        });
        
        req.on('error', reject);
        
        if (options.body) {
            req.write(options.body);
        }
        
        req.end();
    });
}

async function testAIChat() {
    console.log('🤖 Testing AI Chat Functionality...\n');

    try {
        // First, login to get a token
        console.log('🔐 Step 1: Login to get auth token');
        const loginResponse = await makeRequest('http://localhost:3002/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'demo123'
            })
        });

        const loginData = await loginResponse.json();
        
        if (!loginResponse.ok) {
            throw new Error(`Login failed: ${loginData.message}`);
        }

        console.log('✅ Login successful!');
        const token = loginData.token;

        // Test AI chat
        console.log('\n💬 Step 2: Test AI Chat');
        const chatResponse = await makeRequest('http://localhost:3002/api/ai/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                message: 'How am I doing financially? What should I focus on next?'
            })
        });

        const chatData = await chatResponse.json();
        
        if (chatResponse.ok) {
            console.log('✅ AI Chat Response:');
            console.log(chatData.message);
        } else {
            console.log('❌ AI Chat failed:', chatData.message);
        }

        // Test insights generation
        console.log('\n💡 Step 3: Test Insights Generation');
        const insightsResponse = await makeRequest('http://localhost:3002/api/ai/insights/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });

        const insightsData = await insightsResponse.json();
        
        if (insightsResponse.ok) {
            console.log('✅ Generated Insights:');
            console.log(JSON.stringify(insightsData, null, 2));
        } else {
            console.log('❌ Insights generation failed:', insightsData.message);
        }

        // Test scenario analysis
        console.log('\n🎯 Step 4: Test Scenario Analysis');
        const scenarioResponse = await makeRequest('http://localhost:3002/api/ai/scenario/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                scenario: {
                    type: 'job_loss',
                    name: 'Job Loss Scenario',
                    parameters: { months: 6 }
                }
            })
        });

        const scenarioData = await scenarioResponse.json();
        
        if (scenarioResponse.ok) {
            console.log('✅ Scenario Analysis:');
            console.log(JSON.stringify(scenarioData, null, 2));
        } else {
            console.log('❌ Scenario analysis failed:', scenarioData.message);
        }

        console.log('\n🎉 All AI functionality tests completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testAIChat();
