<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test - WealthWise AI</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 40px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        .test-button {
            margin: 10px;
            padding: 12px 24px;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: 500;
        }
        .success { background: #d1fae5; color: #065f46; }
        .error { background: #fee2e2; color: #991b1b; }
        .info { background: #dbeafe; color: #1e40af; }
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 WealthWise AI - Button Functionality Test</h1>
        <p>This page tests all button functionality with detailed console logging.</p>

        <div class="test-section">
            <h2>🔐 Authentication Buttons</h2>
            <button class="btn-secondary test-button" onclick="testLoginModal()">Test Login Modal</button>
            <button class="btn-primary test-button" onclick="testSignupModal()">Test Signup Modal</button>
            <button class="btn-primary test-button" onclick="testFreeTrial()">Test Free Trial</button>
            <div id="auth-status" class="status info">Click buttons above to test authentication</div>
        </div>

        <div class="test-section">
            <h2>📞 Contact Buttons</h2>
            <button class="btn-primary test-button" onclick="testContactSales()">Test Contact Sales</button>
            <div id="contact-status" class="status info">Click button above to test contact sales</div>
        </div>

        <div class="test-section">
            <h2>🧪 Form Submission Tests</h2>
            <button class="btn-primary test-button" onclick="testSignupSubmission()">Test Signup Submission</button>
            <button class="btn-secondary test-button" onclick="testLoginSubmission()">Test Login Submission</button>
            <div id="form-status" class="status info">Click buttons above to test form submissions</div>
        </div>

        <div class="test-section">
            <h2>📊 Console Output</h2>
            <div id="console-output" class="console-output">Console logs will appear here...</div>
            <button class="btn-secondary test-button" onclick="clearConsole()">Clear Console</button>
        </div>

        <div class="test-section">
            <h2>🎯 Test Results</h2>
            <div id="test-results" class="status info">Run tests above to see results</div>
        </div>
    </div>

    <!-- Include all modals from main page -->
    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('loginModal')">&times;</span>
            <h2>Welcome Back</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">Email</label>
                    <input type="email" id="loginEmail" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <input type="password" id="loginPassword" value="demo123">
                </div>
                <button type="submit" class="btn-primary btn-full">Login</button>
            </form>
            <p class="modal-footer">Don't have an account? <a href="#" onclick="showSignupModal()">Sign up</a></p>
        </div>
    </div>

    <!-- Signup Modal -->
    <div id="signupModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('signupModal')">&times;</span>
            <h2>Create Your Account</h2>
            <form id="signupForm">
                <div class="form-group">
                    <label for="signupName">Full Name</label>
                    <input type="text" id="signupName" value="Test User">
                </div>
                <div class="form-group">
                    <label for="signupEmail">Email</label>
                    <input type="email" id="signupEmail" value="">
                </div>
                <div class="form-group">
                    <label for="signupPassword">Password</label>
                    <input type="password" id="signupPassword" value="testpass123">
                </div>
                <button type="submit" class="btn-primary btn-full">Create Account</button>
            </form>
            <p class="modal-footer">Already have an account? <a href="#" onclick="showLoginModal()">Login</a></p>
        </div>
    </div>

    <!-- Contact Sales Modal -->
    <div id="contactSalesModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('contactSalesModal')">&times;</span>
            <h2>Contact Sales</h2>
            <p>Get in touch with our sales team to discuss enterprise solutions and custom pricing.</p>
            <form id="contactSalesForm">
                <div class="form-group">
                    <label for="contactName">Full Name *</label>
                    <input type="text" id="contactName" value="Test Contact">
                </div>
                <div class="form-group">
                    <label for="contactEmail">Business Email *</label>
                    <input type="email" id="contactEmail" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="contactCompany">Company Name *</label>
                    <input type="text" id="contactCompany" value="Test Company">
                </div>
                <div class="form-group">
                    <label for="contactMessage">Message *</label>
                    <textarea id="contactMessage" rows="4">Test message for contact sales</textarea>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="contactConsent" checked>
                        <span class="checkmark"></span>
                        I agree to receive communications from WealthWise AI about products and services.
                    </label>
                </div>
                <button type="submit" class="btn-primary btn-full">Send Message</button>
            </form>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Override console.log to capture output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ef4444' : type === 'warn' ? '#f59e0b' : '#10b981';
            consoleOutput.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // Test functions
        function testLoginModal() {
            console.log('Testing login modal...');
            try {
                showLoginModal();
                updateStatus('auth-status', '✅ Login modal opened successfully', 'success');
            } catch (error) {
                console.error('Login modal test failed:', error);
                updateStatus('auth-status', '❌ Login modal test failed: ' + error.message, 'error');
            }
        }

        function testSignupModal() {
            console.log('Testing signup modal...');
            try {
                showSignupModal();
                updateStatus('auth-status', '✅ Signup modal opened successfully', 'success');
            } catch (error) {
                console.error('Signup modal test failed:', error);
                updateStatus('auth-status', '❌ Signup modal test failed: ' + error.message, 'error');
            }
        }

        function testFreeTrial() {
            console.log('Testing free trial...');
            try {
                startFreeTrial();
                updateStatus('auth-status', '✅ Free trial function executed successfully', 'success');
            } catch (error) {
                console.error('Free trial test failed:', error);
                updateStatus('auth-status', '❌ Free trial test failed: ' + error.message, 'error');
            }
        }

        function testContactSales() {
            console.log('Testing contact sales modal...');
            try {
                showContactSalesModal();
                updateStatus('contact-status', '✅ Contact sales modal opened successfully', 'success');
            } catch (error) {
                console.error('Contact sales test failed:', error);
                updateStatus('contact-status', '❌ Contact sales test failed: ' + error.message, 'error');
            }
        }

        async function testSignupSubmission() {
            console.log('Testing signup form submission...');
            try {
                // Generate unique email
                const email = 'test' + Date.now() + '@example.com';
                document.getElementById('signupEmail').value = email;
                
                // Trigger form submission
                const form = document.getElementById('signupForm');
                const event = new Event('submit', { bubbles: true, cancelable: true });
                form.dispatchEvent(event);
                
                updateStatus('form-status', '✅ Signup form submission triggered', 'success');
            } catch (error) {
                console.error('Signup submission test failed:', error);
                updateStatus('form-status', '❌ Signup submission test failed: ' + error.message, 'error');
            }
        }

        async function testLoginSubmission() {
            console.log('Testing login form submission...');
            try {
                // Trigger form submission
                const form = document.getElementById('loginForm');
                const event = new Event('submit', { bubbles: true, cancelable: true });
                form.dispatchEvent(event);
                
                updateStatus('form-status', '✅ Login form submission triggered', 'success');
            } catch (error) {
                console.error('Login submission test failed:', error);
                updateStatus('form-status', '❌ Login submission test failed: ' + error.message, 'error');
            }
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }

        function clearConsole() {
            consoleOutput.innerHTML = 'Console cleared...';
        }

        // Initialize
        console.log('Button test page loaded successfully');
        console.log('Server should be running on http://localhost:3002');
        console.log('Ready to test all button functionality');
    </script>
</body>
</html>
