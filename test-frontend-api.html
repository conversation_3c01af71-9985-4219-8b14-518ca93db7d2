<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Test - WealthWise AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .test-button:hover {
            background: #5855eb;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <h1>🧪 Frontend API Test - WealthWise AI</h1>
    <p>This page tests API calls from the frontend using fetch().</p>

    <div class="test-section">
        <h2>🔍 Health Check Test</h2>
        <button class="test-button" onclick="testHealthCheck()">Test Health Check</button>
        <div id="health-result" class="result info">Click button to test health check endpoint</div>
    </div>

    <div class="test-section">
        <h2>👤 Signup Test</h2>
        <button class="test-button" onclick="testSignup()">Test Signup</button>
        <div id="signup-result" class="result info">Click button to test signup endpoint</div>
    </div>

    <div class="test-section">
        <h2>🔐 Login Test</h2>
        <button class="test-button" onclick="testLogin()">Test Login</button>
        <div id="login-result" class="result info">Click button to test login endpoint</div>
    </div>

    <div class="test-section">
        <h2>📞 Contact Sales Test</h2>
        <button class="test-button" onclick="testContactSales()">Test Contact Sales</button>
        <div id="contact-result" class="result info">Click button to test contact sales endpoint</div>
    </div>

    <div class="test-section">
        <h2>🌐 Network Information</h2>
        <div id="network-info" class="result info">
Current URL: <span id="current-url"></span>
Base URL: <span id="base-url"></span>
User Agent: <span id="user-agent"></span>
        </div>
    </div>

    <script>
        // Display network information
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('base-url').textContent = window.location.origin;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        async function testHealthCheck() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.textContent = 'Testing health check...';
            resultDiv.className = 'result info';

            try {
                console.log('Testing health check endpoint...');
                
                const response = await fetch('/api/health', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                console.log('Health check response:', response);
                console.log('Status:', response.status);
                console.log('OK:', response.ok);

                const data = await response.json();
                console.log('Health check data:', data);

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ SUCCESS\nStatus: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ FAILED\nStatus: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                console.error('Health check error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ NETWORK ERROR\nError: ${error.message}\nStack: ${error.stack}`;
            }
        }

        async function testSignup() {
            const resultDiv = document.getElementById('signup-result');
            resultDiv.textContent = 'Testing signup...';
            resultDiv.className = 'result info';

            try {
                const signupData = {
                    name: 'Frontend Test User',
                    email: 'frontend' + Date.now() + '@example.com',
                    password: 'testpass123'
                };

                console.log('Testing signup with data:', signupData);

                const response = await fetch('/api/auth/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(signupData)
                });

                console.log('Signup response:', response);
                console.log('Status:', response.status);
                console.log('OK:', response.ok);

                const data = await response.json();
                console.log('Signup data:', data);

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ SUCCESS\nStatus: ${response.status}\nUser: ${data.user.name}\nEmail: ${data.user.email}\nToken: ${data.token ? 'Generated' : 'Missing'}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ FAILED\nStatus: ${response.status}\nMessage: ${data.message}`;
                }
            } catch (error) {
                console.error('Signup error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ NETWORK ERROR\nError: ${error.message}\nStack: ${error.stack}`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.textContent = 'Testing login...';
            resultDiv.className = 'result info';

            try {
                const loginData = {
                    email: '<EMAIL>',
                    password: 'demo123'
                };

                console.log('Testing login with data:', loginData);

                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                console.log('Login response:', response);
                console.log('Status:', response.status);
                console.log('OK:', response.ok);

                const data = await response.json();
                console.log('Login data:', data);

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ SUCCESS\nStatus: ${response.status}\nUser: ${data.user.name}\nPlan: ${data.user.planType}\nToken: ${data.token ? 'Generated' : 'Missing'}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ FAILED\nStatus: ${response.status}\nMessage: ${data.message}`;
                }
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ NETWORK ERROR\nError: ${error.message}\nStack: ${error.stack}`;
            }
        }

        async function testContactSales() {
            const resultDiv = document.getElementById('contact-result');
            resultDiv.textContent = 'Testing contact sales...';
            resultDiv.className = 'result info';

            try {
                const contactData = {
                    name: 'Frontend Test Contact',
                    email: '<EMAIL>',
                    company: 'Frontend Test Company',
                    message: 'This is a test message from the frontend',
                    consent: true
                };

                console.log('Testing contact sales with data:', contactData);

                const response = await fetch('/api/contact/sales', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(contactData)
                });

                console.log('Contact sales response:', response);
                console.log('Status:', response.status);
                console.log('OK:', response.ok);

                const data = await response.json();
                console.log('Contact sales data:', data);

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ SUCCESS\nStatus: ${response.status}\nMessage: ${data.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ FAILED\nStatus: ${response.status}\nMessage: ${data.message}`;
                }
            } catch (error) {
                console.error('Contact sales error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ NETWORK ERROR\nError: ${error.message}\nStack: ${error.stack}`;
            }
        }

        // Test on page load
        console.log('Frontend API test page loaded');
        console.log('Current URL:', window.location.href);
        console.log('Ready to test API endpoints');
    </script>
</body>
</html>
