{"version": 3, "file": "batches.js", "sourceRoot": "", "sources": ["../../../src/resources/beta/messages/batches.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;AAEtF,wDAAqD;AAIrD,4DAA8E;AAC9E,0DAAyD;AAEzD,+DAAgE;AAChE,6CAAgD;AAChD,0DAAoD;AAEpD,MAAa,OAAQ,SAAQ,sBAAW;IACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4BG;IACH,MAAM,CAAC,MAAyB,EAAE,OAAwB;QACxD,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE;YACzD,IAAI;YACJ,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,QAAQ,CACN,cAAsB,EACtB,SAAiD,EAAE,EACnD,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAA,WAAI,EAAA,wBAAwB,cAAc,YAAY,EAAE;YAC9E,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,IAAI,CACF,SAA6C,EAAE,EAC/C,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,gCAAgC,EAAE,CAAA,iBAAsB,CAAA,EAAE;YACvF,KAAK;YACL,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,MAAM,CACJ,cAAsB,EACtB,SAA+C,EAAE,EACjD,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAA,WAAI,EAAA,wBAAwB,cAAc,YAAY,EAAE;YACjF,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,MAAM,CACJ,cAAsB,EACtB,SAA+C,EAAE,EACjD,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAA,WAAI,EAAA,wBAAwB,cAAc,mBAAmB,EAAE;YACtF,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjF,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,OAAO,CACX,cAAsB,EACtB,SAAyC,EAAE,EAC3C,OAAwB;QAExB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACvB,MAAM,IAAI,sBAAc,CACtB,yDAAyD,KAAK,CAAC,iBAAiB,MAAM,KAAK,CAAC,EAAE,EAAE,CACjG,CAAC;QACJ,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO;aAChB,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE;YACtB,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB;oBACE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,4BAA4B,CAAC,CAAC,QAAQ,EAAE;oBAC7E,MAAM,EAAE,oBAAoB;iBAC7B;gBACD,OAAO,EAAE,OAAO;aACjB,CAAC;YACF,MAAM,EAAE,IAAI;YACZ,gBAAgB,EAAE,IAAI;SACvB,CAAC;aACD,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,oBAAY,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,UAAU,CAAC,CAEvF,CAAC;IACJ,CAAC;CACF;AA5ND,0BA4NC"}