{"version": 3, "file": "parse.d.ts", "sourceRoot": "", "sources": ["../src/internal/parse.ts"], "names": [], "mappings": "OAEO,KAAK,EAAE,mBAAmB,EAAE;OAE5B,EAAE,KAAK,aAAa,EAAE;OAEtB,KAAK,EAAE,YAAY,EAAE;AAE5B,MAAM,MAAM,gBAAgB,GAAG;IAC7B,QAAQ,EAAE,QAAQ,CAAC;IACnB,OAAO,EAAE,mBAAmB,CAAC;IAC7B,UAAU,EAAE,eAAe,CAAC;IAC5B,YAAY,EAAE,MAAM,CAAC;IACrB,mBAAmB,EAAE,MAAM,GAAG,SAAS,CAAC;IACxC,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,wBAAsB,oBAAoB,CAAC,CAAC,EAC1C,MAAM,EAAE,aAAa,EACrB,KAAK,EAAE,gBAAgB,GACtB,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CA+C3B;AAED,MAAM,MAAM,aAAa,CAAC,CAAC,IACzB,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,GACrD,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG;IAAE,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;CAAE,GACnE,CAAC,CAAC;AAEN,wBAAgB,YAAY,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,CAS9E"}