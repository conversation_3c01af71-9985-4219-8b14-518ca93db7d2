/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #10b981;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: var(--surface-color);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: 28px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

.btn-full {
    width: 100%;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
}

.hero-subtitle {
    font-size: 20px;
    opacity: 0.9;
    margin-bottom: 40px;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 60px;
}

.hero-stats {
    display: flex;
    gap: 40px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 32px;
    font-weight: 700;
    color: var(--accent-color);
}

.stat-label {
    font-size: 14px;
    opacity: 0.8;
}

/* Dashboard Preview */
.dashboard-preview {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
}

.preview-header {
    background: #f1f5f9;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.preview-dots {
    display: flex;
    gap: 8px;
}

.preview-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #cbd5e1;
}

.preview-dots span:first-child {
    background: #ef4444;
}

.preview-dots span:nth-child(2) {
    background: #f59e0b;
}

.preview-dots span:last-child {
    background: #10b981;
}

.preview-content {
    padding: 30px;
}

.preview-chart {
    height: 120px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    opacity: 0.8;
}

.preview-cards {
    display: flex;
    gap: 15px;
}

.preview-card {
    flex: 1;
    height: 60px;
    background: var(--background-color);
    border-radius: var(--border-radius);
}

/* Features Section */
.features {
    padding: 100px 0;
    background: var(--surface-color);
}

.section-title {
    font-size: 40px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 18px;
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 60px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.feature-card {
    background: var(--surface-color);
    padding: 40px 30px;
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 32px;
}

.feature-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Pricing Section */
.pricing {
    padding: 100px 0;
    background: var(--background-color);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--surface-color);
    border-radius: 16px;
    padding: 40px 30px;
    box-shadow: var(--shadow-md);
    position: relative;
    border: 2px solid transparent;
    transition: var(--transition);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.pricing-header {
    text-align: center;
    margin-bottom: 30px;
}

.pricing-header h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 5px;
}

.currency {
    font-size: 20px;
    color: var(--text-secondary);
}

.amount {
    font-size: 48px;
    font-weight: 700;
    color: var(--primary-color);
}

.period {
    color: var(--text-secondary);
}

.pricing-features {
    list-style: none;
    margin-bottom: 30px;
}

.pricing-features li {
    padding: 10px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.pricing-features i {
    color: var(--accent-color);
    width: 20px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--surface-color);
    margin: 5% auto;
    padding: 40px;
    border-radius: 16px;
    width: 90%;
    max-width: 400px;
    position: relative;
    box-shadow: var(--shadow-lg);
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: var(--text-secondary);
}

.close:hover {
    color: var(--text-primary);
}

.modal-content h2 {
    margin-bottom: 30px;
    text-align: center;
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.modal-footer {
    text-align: center;
    margin-top: 20px;
    color: var(--text-secondary);
}

.modal-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.modal-footer a:hover {
    text-decoration: underline;
}

/* Contact Sales Modal Specific Styles */
.contact-info {
    margin-top: 20px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #6366f1;
}

.contact-info p {
    margin: 8px 0;
    color: #64748b;
}

.contact-info p:first-child {
    color: #1e293b;
    margin-bottom: 12px;
}

/* Checkbox styling */
.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
    accent-color: #6366f1;
}

.checkbox-label .checkmark {
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    background: white;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #6366f1;
    border-color: #6366f1;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Select dropdown styling */
select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

select:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Textarea styling */
textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
    transition: border-color 0.3s ease;
}

textarea:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

textarea::placeholder {
    color: #9ca3af;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.footer-brand i {
    font-size: 1.5rem;
    color: #6366f1;
}

.footer-brand span {
    font-size: 1.5rem;
    font-weight: 700;
}

.footer-section p {
    color: #94a3b8;
    line-height: 1.6;
    margin: 0;
}

.footer-section h4 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #6366f1;
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 20px;
    text-align: center;
}

.footer-bottom p {
    color: #94a3b8;
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 36px;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .nav-menu {
        display: none;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-card.featured {
        transform: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }
}
