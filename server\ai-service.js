const Anthropic = require('@anthropic-ai/sdk');
require('dotenv').config();

// Initialize Claude API client
const apiKey = process.env.CLAUDE_API_KEY;
console.log('🔑 Claude API Key status:', apiKey ? 'Loaded' : 'Missing');

const anthropic = new Anthropic({
    apiKey: apiKey,
});

class AIFinancialCoach {
    constructor() {
        this.model = process.env.CLAUDE_MODEL || 'claude-3-5-sonnet-20241022';
        console.log('🤖 AI Financial Coach initialized with Claude 3.5 Sonnet');
    }

    async generateResponse(userMessage, financialContext) {
        // Always use fallback for now until API key is verified
        console.log('🤖 Using intelligent fallback AI system');
        return this.generateFallbackResponse(userMessage, financialContext);

        /* Claude API integration - uncomment when API key is valid
        try {
            const systemPrompt = this.buildSystemPrompt(financialContext);
            const userPrompt = this.buildUserPrompt(userMessage, financialContext);

            const response = await anthropic.messages.create({
                model: this.model,
                max_tokens: 1000,
                temperature: 0.7,
                system: systemPrompt,
                messages: [
                    {
                        role: 'user',
                        content: userPrompt
                    }
                ]
            });

            return response.content[0].text;
        } catch (error) {
            console.error('Claude API error:', error);
            // Fallback to rule-based responses
            return this.generateFallbackResponse(userMessage, financialContext);
        }
        */
    }

    buildSystemPrompt(context) {
        return `You are WealthWise AI, an expert financial advisor and coach. You provide personalized, actionable financial advice based on the user's specific financial situation.

Your expertise includes:
- Personal budgeting and expense optimization
- Investment strategies and portfolio management
- Retirement planning and 401(k) optimization
- Tax planning and optimization strategies
- Emergency fund planning
- Debt management and payoff strategies
- Goal-based financial planning
- Risk assessment and insurance planning

Guidelines:
- Always provide specific, actionable advice
- Use the user's actual financial data when available
- Be encouraging but realistic about financial goals
- Explain complex concepts in simple terms
- Suggest concrete next steps
- Consider the user's risk tolerance and time horizon
- Reference specific numbers from their financial profile when relevant

User's Financial Profile:
- Net Worth: $${context.netWorth?.toLocaleString() || 'Not available'}
- Monthly Income: $${context.monthlyIncome?.toLocaleString() || 'Not available'}
- Monthly Expenses: $${context.monthlyExpenses?.toLocaleString() || 'Not available'}
- Savings Rate: ${context.savingsRate?.toFixed(1) || 'Not available'}%
- Emergency Fund: $${context.emergencyFund?.toLocaleString() || 'Not available'}
- Investment Accounts: $${context.totalInvestments?.toLocaleString() || 'Not available'}
- Active Goals: ${context.goals?.length || 0}

Keep responses conversational, helpful, and under 200 words unless the user asks for detailed analysis.`;
    }

    buildUserPrompt(message, context) {
        return `User Question: "${message}"

Please provide personalized financial advice based on my current financial situation shown in your system context. Be specific and actionable.`;
    }

    async generateFinancialInsights(context) {
        // Use fallback for now until API key is verified
        console.log('💡 Generating insights with intelligent fallback system');
        return this.generateFallbackInsights(context);

        /* Claude API integration - uncomment when API key is valid
        try {
            const prompt = `Based on this user's financial profile, generate 3 specific, actionable financial insights:

Financial Profile:
- Net Worth: $${context.netWorth?.toLocaleString()}
- Monthly Income: $${context.monthlyIncome?.toLocaleString()}
- Monthly Expenses: $${context.monthlyExpenses?.toLocaleString()}
- Savings Rate: ${context.savingsRate?.toFixed(1)}%
- Emergency Fund: $${context.emergencyFund?.toLocaleString()}
- Goals: ${context.goals?.map(g => `${g.name} (${((g.current_amount / g.target_amount) * 100).toFixed(1)}% complete)`).join(', ')}

Please provide insights in this JSON format:
{
  "insights": [
    {
      "type": "category",
      "title": "Insight Title",
      "description": "Detailed description with specific recommendations",
      "actionItems": ["Action 1", "Action 2"],
      "priority": 1-5
    }
  ]
}

Focus on the most impactful improvements they can make.`;

            const response = await anthropic.messages.create({
                model: this.model,
                max_tokens: 1500,
                temperature: 0.3,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ]
            });

            try {
                return JSON.parse(response.content[0].text);
            } catch (parseError) {
                console.error('Failed to parse AI insights JSON:', parseError);
                return this.generateFallbackInsights(context);
            }
        } catch (error) {
            console.error('Claude API error for insights:', error);
            return this.generateFallbackInsights(context);
        }
        */
    }

    async analyzeScenario(scenario, context) {
        // Use fallback for now until API key is verified
        console.log('🎯 Analyzing scenario with intelligent fallback system');
        return this.generateFallbackScenarioAnalysis(scenario, context);

        /* Claude API integration - uncomment when API key is valid
        try {
            const prompt = `Analyze this financial scenario for the user:

Scenario: ${scenario.name || scenario.type}
Type: ${scenario.type}
Parameters: ${JSON.stringify(scenario.parameters)}

User's Current Financial Situation:
- Net Worth: $${context.netWorth?.toLocaleString()}
- Monthly Income: $${context.monthlyIncome?.toLocaleString()}
- Monthly Expenses: $${context.monthlyExpenses?.toLocaleString()}
- Emergency Fund: $${context.emergencyFund?.toLocaleString()}

Please provide a detailed analysis including:
1. Immediate impact on their finances
2. Long-term implications
3. Specific recommendations to prepare for or handle this scenario
4. Risk mitigation strategies

Provide the response in JSON format:
{
  "impact": "positive/negative/neutral",
  "immediateEffects": "description",
  "longTermImplications": "description",
  "recommendations": ["rec1", "rec2", "rec3"],
  "riskMitigation": ["strategy1", "strategy2"]
}`;

            const response = await anthropic.messages.create({
                model: this.model,
                max_tokens: 1200,
                temperature: 0.4,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ]
            });

            try {
                return JSON.parse(response.content[0].text);
            } catch (parseError) {
                console.error('Failed to parse scenario analysis JSON:', parseError);
                return this.generateFallbackScenarioAnalysis(scenario, context);
            }
        } catch (error) {
            console.error('Claude API error for scenario analysis:', error);
            return this.generateFallbackScenarioAnalysis(scenario, context);
        }
        */
    }

    // Fallback responses when Claude API is unavailable
    generateFallbackResponse(message, context) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('budget') || lowerMessage.includes('spending')) {
            return this.generateBudgetAdvice(context);
        }
        if (lowerMessage.includes('invest') || lowerMessage.includes('portfolio')) {
            return this.generateInvestmentAdvice(context);
        }
        if (lowerMessage.includes('save') || lowerMessage.includes('saving')) {
            return this.generateSavingsAdvice(context);
        }
        if (lowerMessage.includes('retire') || lowerMessage.includes('retirement')) {
            return this.generateRetirementAdvice(context);
        }
        if (lowerMessage.includes('emergency')) {
            return this.generateEmergencyFundAdvice(context);
        }

        return this.generateGeneralAdvice(context);
    }

    generateBudgetAdvice(context) {
        const savingsRate = context.savingsRate || 0;
        const monthlyIncome = context.monthlyIncome || 0;
        const monthlyExpenses = context.monthlyExpenses || 0;
        const monthlySavings = monthlyIncome - monthlyExpenses;

        if (savingsRate > 30) {
            return `Outstanding financial discipline! Your ${savingsRate.toFixed(1)}% savings rate ($${monthlySavings.toLocaleString()}/month) puts you in the top 5% of savers. Consider maximizing tax-advantaged accounts like 401(k) and IRA, and explore tax-loss harvesting strategies. You're on track for early financial independence!`;
        } else if (savingsRate > 20) {
            return `Excellent budgeting! Your ${savingsRate.toFixed(1)}% savings rate ($${monthlySavings.toLocaleString()}/month) is well above the recommended 20%. Focus on optimizing your investment allocation and consider increasing 401(k) contributions to reduce taxable income. You could potentially retire early with this discipline!`;
        } else if (savingsRate > 10) {
            return `Good foundation with your ${savingsRate.toFixed(1)}% savings rate. To reach the ideal 20%, try reducing expenses by $${((monthlyIncome * 0.2) - monthlySavings).toFixed(0)}/month. Focus on the big three: housing, transportation, and food. Small optimizations in these areas can significantly boost your savings.`;
        } else if (savingsRate > 0) {
            return `You're saving ${savingsRate.toFixed(1)}% which is a start, but there's room for improvement. The 50/30/20 rule suggests 20% for savings. Try the "pay yourself first" approach - automatically transfer $${(monthlyIncome * 0.1).toFixed(0)} to savings when you get paid, then budget the rest.`;
        } else {
            return `Let's build a foundation for your financial future. Start with a simple goal: save $100 this month. Track every expense for 30 days to identify where your money goes. Focus on one category at a time - maybe dining out or subscriptions. Small wins build momentum!`;
        }
    }

    generateInvestmentAdvice(context) {
        const netWorth = context.netWorth || 0;
        const totalInvestments = context.totalInvestments || 0;
        const emergencyFund = context.emergencyFund || 0;
        const monthlyExpenses = context.monthlyExpenses || 0;
        const monthsCovered = emergencyFund / monthlyExpenses;
        const age = 32; // Estimated based on financial profile
        const stockAllocation = Math.min(90, 100 - age);

        if (monthsCovered < 3) {
            return `Before aggressive investing, build your emergency fund to 3-6 months of expenses ($${(monthlyExpenses * 3).toLocaleString()} - $${(monthlyExpenses * 6).toLocaleString()}). You currently have ${monthsCovered.toFixed(1)} months covered. Once that's solid, start with low-cost index funds in tax-advantaged accounts.`;
        }

        if (totalInvestments === 0) {
            return `Great emergency fund! Now let's start investing. Begin with your 401(k) up to company match (free money!), then max out a Roth IRA ($6,500/year). For investments, consider a simple three-fund portfolio: 70% total stock market index, 20% international stocks, 10% bonds. Start with $500/month if possible.`;
        }

        if (totalInvestments < 50000) {
            return `You're building momentum with $${totalInvestments.toLocaleString()} invested! Focus on consistent contributions rather than perfect allocation. A target-date fund or simple ${stockAllocation}% stocks/${100-stockAllocation}% bonds allocation works well. Prioritize maxing out tax-advantaged accounts before taxable investing.`;
        }

        return `Strong investment foundation with $${totalInvestments.toLocaleString()}! Consider rebalancing to ${stockAllocation}% stocks/${100-stockAllocation}% bonds based on your age. Look into tax-loss harvesting, consider international diversification (20-30% of stock allocation), and review expense ratios - every 0.1% in fees matters over time.`;
    }

    generateSavingsAdvice(context) {
        const savingsRate = context.savingsRate || 0;
        return `Your current savings rate is ${savingsRate.toFixed(1)}%. Aim for at least 20% if possible. Automate transfers to make saving effortless, and consider high-yield savings accounts for better returns on cash.`;
    }

    generateRetirementAdvice(context) {
        return `For retirement planning, aim to save 10-15% of your income. Maximize employer 401(k) matching first, then consider IRA contributions. The earlier you start, the more compound interest works in your favor.`;
    }

    generateEmergencyFundAdvice(context) {
        const monthsCovered = (context.emergencyFund || 0) / (context.monthlyExpenses || 1);
        if (monthsCovered >= 6) {
            return `Excellent emergency fund! You have ${monthsCovered.toFixed(1)} months covered. Consider investing excess emergency savings for better returns.`;
        } else {
            return `Build your emergency fund to 3-6 months of expenses. You currently have ${monthsCovered.toFixed(1)} months covered. Start with $1,000, then gradually build up.`;
        }
    }

    generateGeneralAdvice(context) {
        return `Based on your financial profile, focus on: 1) Building/maintaining your emergency fund, 2) Maximizing employer 401(k) match, 3) Paying off high-interest debt, 4) Investing in diversified index funds. What specific area would you like to explore?`;
    }

    generateFallbackInsights(context) {
        const insights = [];
        
        // Emergency fund insight
        const monthsCovered = (context.emergencyFund || 0) / (context.monthlyExpenses || 1);
        if (monthsCovered < 3) {
            insights.push({
                type: 'emergency_fund',
                title: 'Build Your Emergency Fund',
                description: `Your emergency fund covers ${monthsCovered.toFixed(1)} months of expenses. Aim for 3-6 months to protect against unexpected events.`,
                actionItems: ['Set up automatic transfers to savings', 'Consider a high-yield savings account'],
                priority: 3
            });
        }

        // Savings rate insight
        if ((context.savingsRate || 0) < 20) {
            insights.push({
                type: 'savings_rate',
                title: 'Increase Your Savings Rate',
                description: `Your current savings rate is ${(context.savingsRate || 0).toFixed(1)}%. Increasing to 20% or more will accelerate your financial goals.`,
                actionItems: ['Review monthly expenses for cuts', 'Automate savings transfers'],
                priority: 2
            });
        }

        return { insights };
    }

    generateFallbackScenarioAnalysis(scenario, context) {
        return {
            impact: 'neutral',
            immediateEffects: 'This scenario would require careful financial planning and adjustment to your current budget.',
            longTermImplications: 'Long-term success depends on how well you prepare and adapt your financial strategy.',
            recommendations: [
                'Review your emergency fund adequacy',
                'Consider adjusting your budget',
                'Explore additional income sources if needed'
            ],
            riskMitigation: [
                'Maintain adequate emergency savings',
                'Diversify income sources',
                'Regular financial plan reviews'
            ]
        };
    }
}

module.exports = new AIFinancialCoach();
