const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

// Initialize Gemini API client
const apiKey = process.env.GEMINI_API_KEY;
console.log('🔑 Gemini API Key status:', apiKey ? 'Loaded' : 'Missing');

const genAI = new GoogleGenerativeAI(apiKey);

class AIFinancialCoach {
    constructor() {
        this.modelName = process.env.GEMINI_MODEL || 'gemini-2.0-flash-exp';
        this.model = genAI.getGenerativeModel({ model: this.modelName });
        console.log('🤖 AI Financial Coach initialized with Gemini 2.0 Flash');
    }

    async generateResponse(userMessage, financialContext) {
        try {
            console.log('🤖 Generating response with Gemini 2.0 Flash');

            const systemPrompt = this.buildSystemPrompt(financialContext);
            const fullPrompt = `${systemPrompt}\n\nUser Question: "${userMessage}"\n\nPlease provide personalized financial advice based on the user's financial situation. Be specific, actionable, and encouraging.`;

            const result = await this.model.generateContent({
                contents: [{ role: 'user', parts: [{ text: fullPrompt }] }],
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 1000,
                }
            });

            const response = await result.response;
            return response.text();
        } catch (error) {
            console.error('Gemini API error:', error);
            console.log('🔄 Falling back to intelligent rule-based system');
            return this.generateFallbackResponse(userMessage, financialContext);
        }
    }

    buildSystemPrompt(context) {
        return `You are WealthWise AI, an expert financial advisor and coach. You provide personalized, actionable financial advice based on the user's specific financial situation.

Your expertise includes:
- Personal budgeting and expense optimization
- Investment strategies and portfolio management
- Retirement planning and 401(k) optimization
- Tax planning and optimization strategies
- Emergency fund planning
- Debt management and payoff strategies
- Goal-based financial planning
- Risk assessment and insurance planning

Guidelines:
- Always provide specific, actionable advice
- Use the user's actual financial data when available
- Be encouraging but realistic about financial goals
- Explain complex concepts in simple terms
- Suggest concrete next steps
- Consider the user's risk tolerance and time horizon
- Reference specific numbers from their financial profile when relevant

User's Financial Profile:
- Net Worth: $${context.netWorth?.toLocaleString() || 'Not available'}
- Monthly Income: $${context.monthlyIncome?.toLocaleString() || 'Not available'}
- Monthly Expenses: $${context.monthlyExpenses?.toLocaleString() || 'Not available'}
- Savings Rate: ${context.savingsRate?.toFixed(1) || 'Not available'}%
- Emergency Fund: $${context.emergencyFund?.toLocaleString() || 'Not available'}
- Investment Accounts: $${context.totalInvestments?.toLocaleString() || 'Not available'}
- Active Goals: ${context.goals?.length || 0}

Keep responses conversational, helpful, and under 200 words unless the user asks for detailed analysis.`;
    }

    buildUserPrompt(message, context) {
        return `User Question: "${message}"

Please provide personalized financial advice based on my current financial situation shown in your system context. Be specific and actionable.`;
    }

    async generateFinancialInsights(context) {
        try {
            console.log('💡 Generating insights with Gemini 2.0 Flash');

            const prompt = `Based on this user's financial profile, generate 3 specific, actionable financial insights:

Financial Profile:
- Net Worth: $${context.netWorth?.toLocaleString()}
- Monthly Income: $${context.monthlyIncome?.toLocaleString()}
- Monthly Expenses: $${context.monthlyExpenses?.toLocaleString()}
- Savings Rate: ${context.savingsRate?.toFixed(1)}%
- Emergency Fund: $${context.emergencyFund?.toLocaleString()}
- Goals: ${context.goals?.map(g => `${g.name} (${((g.current_amount / g.target_amount) * 100).toFixed(1)}% complete)`).join(', ')}

Please provide insights in this JSON format:
{
  "insights": [
    {
      "type": "category",
      "title": "Insight Title",
      "description": "Detailed description with specific recommendations",
      "actionItems": ["Action 1", "Action 2"],
      "priority": 1-5
    }
  ]
}

Focus on the most impactful improvements they can make. Return only valid JSON.`;

            const result = await this.model.generateContent({
                contents: [{ role: 'user', parts: [{ text: prompt }] }],
                generationConfig: {
                    temperature: 0.3,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 1500,
                }
            });

            const response = await result.response;
            const responseText = response.text();

            try {
                // Clean up the response to extract JSON
                const jsonMatch = responseText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    return JSON.parse(jsonMatch[0]);
                } else {
                    throw new Error('No JSON found in response');
                }
            } catch (parseError) {
                console.error('Failed to parse AI insights JSON:', parseError);
                console.log('Raw response:', responseText);
                return this.generateFallbackInsights(context);
            }
        } catch (error) {
            console.error('Gemini API error for insights:', error);
            console.log('🔄 Falling back to rule-based insights');
            return this.generateFallbackInsights(context);
        }
    }

    async analyzeScenario(scenario, context) {
        try {
            console.log('🎯 Analyzing scenario with Gemini 2.0 Flash');

            const prompt = `Analyze this financial scenario for the user:

Scenario: ${scenario.name || scenario.type}
Type: ${scenario.type}
Parameters: ${JSON.stringify(scenario.parameters)}

User's Current Financial Situation:
- Net Worth: $${context.netWorth?.toLocaleString()}
- Monthly Income: $${context.monthlyIncome?.toLocaleString()}
- Monthly Expenses: $${context.monthlyExpenses?.toLocaleString()}
- Emergency Fund: $${context.emergencyFund?.toLocaleString()}

Please provide a detailed analysis including:
1. Immediate impact on their finances
2. Long-term implications
3. Specific recommendations to prepare for or handle this scenario
4. Risk mitigation strategies

Provide the response in JSON format:
{
  "impact": "positive/negative/neutral",
  "immediateEffects": "description",
  "longTermImplications": "description",
  "recommendations": ["rec1", "rec2", "rec3"],
  "riskMitigation": ["strategy1", "strategy2"]
}

Return only valid JSON.`;

            const result = await this.model.generateContent({
                contents: [{ role: 'user', parts: [{ text: prompt }] }],
                generationConfig: {
                    temperature: 0.4,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 1200,
                }
            });

            const response = await result.response;
            const responseText = response.text();

            try {
                // Clean up the response to extract JSON
                const jsonMatch = responseText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    return JSON.parse(jsonMatch[0]);
                } else {
                    throw new Error('No JSON found in response');
                }
            } catch (parseError) {
                console.error('Failed to parse scenario analysis JSON:', parseError);
                console.log('Raw response:', responseText);
                return this.generateFallbackScenarioAnalysis(scenario, context);
            }
        } catch (error) {
            console.error('Gemini API error for scenario analysis:', error);
            console.log('🔄 Falling back to rule-based analysis');
            return this.generateFallbackScenarioAnalysis(scenario, context);
        }
    }

    // Fallback responses when Claude API is unavailable
    generateFallbackResponse(message, context) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('budget') || lowerMessage.includes('spending')) {
            return this.generateBudgetAdvice(context);
        }
        if (lowerMessage.includes('invest') || lowerMessage.includes('portfolio')) {
            return this.generateInvestmentAdvice(context);
        }
        if (lowerMessage.includes('save') || lowerMessage.includes('saving')) {
            return this.generateSavingsAdvice(context);
        }
        if (lowerMessage.includes('retire') || lowerMessage.includes('retirement')) {
            return this.generateRetirementAdvice(context);
        }
        if (lowerMessage.includes('emergency')) {
            return this.generateEmergencyFundAdvice(context);
        }

        return this.generateGeneralAdvice(context);
    }

    generateBudgetAdvice(context) {
        const savingsRate = context.savingsRate || 0;
        const monthlyIncome = context.monthlyIncome || 0;
        const monthlyExpenses = context.monthlyExpenses || 0;
        const monthlySavings = monthlyIncome - monthlyExpenses;

        if (savingsRate > 30) {
            return `Outstanding financial discipline! Your ${savingsRate.toFixed(1)}% savings rate ($${monthlySavings.toLocaleString()}/month) puts you in the top 5% of savers. Consider maximizing tax-advantaged accounts like 401(k) and IRA, and explore tax-loss harvesting strategies. You're on track for early financial independence!`;
        } else if (savingsRate > 20) {
            return `Excellent budgeting! Your ${savingsRate.toFixed(1)}% savings rate ($${monthlySavings.toLocaleString()}/month) is well above the recommended 20%. Focus on optimizing your investment allocation and consider increasing 401(k) contributions to reduce taxable income. You could potentially retire early with this discipline!`;
        } else if (savingsRate > 10) {
            return `Good foundation with your ${savingsRate.toFixed(1)}% savings rate. To reach the ideal 20%, try reducing expenses by $${((monthlyIncome * 0.2) - monthlySavings).toFixed(0)}/month. Focus on the big three: housing, transportation, and food. Small optimizations in these areas can significantly boost your savings.`;
        } else if (savingsRate > 0) {
            return `You're saving ${savingsRate.toFixed(1)}% which is a start, but there's room for improvement. The 50/30/20 rule suggests 20% for savings. Try the "pay yourself first" approach - automatically transfer $${(monthlyIncome * 0.1).toFixed(0)} to savings when you get paid, then budget the rest.`;
        } else {
            return `Let's build a foundation for your financial future. Start with a simple goal: save $100 this month. Track every expense for 30 days to identify where your money goes. Focus on one category at a time - maybe dining out or subscriptions. Small wins build momentum!`;
        }
    }

    generateInvestmentAdvice(context) {
        const netWorth = context.netWorth || 0;
        const totalInvestments = context.totalInvestments || 0;
        const emergencyFund = context.emergencyFund || 0;
        const monthlyExpenses = context.monthlyExpenses || 0;
        const monthsCovered = emergencyFund / monthlyExpenses;
        const age = 32; // Estimated based on financial profile
        const stockAllocation = Math.min(90, 100 - age);

        if (monthsCovered < 3) {
            return `Before aggressive investing, build your emergency fund to 3-6 months of expenses ($${(monthlyExpenses * 3).toLocaleString()} - $${(monthlyExpenses * 6).toLocaleString()}). You currently have ${monthsCovered.toFixed(1)} months covered. Once that's solid, start with low-cost index funds in tax-advantaged accounts.`;
        }

        if (totalInvestments === 0) {
            return `Great emergency fund! Now let's start investing. Begin with your 401(k) up to company match (free money!), then max out a Roth IRA ($6,500/year). For investments, consider a simple three-fund portfolio: 70% total stock market index, 20% international stocks, 10% bonds. Start with $500/month if possible.`;
        }

        if (totalInvestments < 50000) {
            return `You're building momentum with $${totalInvestments.toLocaleString()} invested! Focus on consistent contributions rather than perfect allocation. A target-date fund or simple ${stockAllocation}% stocks/${100-stockAllocation}% bonds allocation works well. Prioritize maxing out tax-advantaged accounts before taxable investing.`;
        }

        return `Strong investment foundation with $${totalInvestments.toLocaleString()}! Consider rebalancing to ${stockAllocation}% stocks/${100-stockAllocation}% bonds based on your age. Look into tax-loss harvesting, consider international diversification (20-30% of stock allocation), and review expense ratios - every 0.1% in fees matters over time.`;
    }

    generateSavingsAdvice(context) {
        const savingsRate = context.savingsRate || 0;
        return `Your current savings rate is ${savingsRate.toFixed(1)}%. Aim for at least 20% if possible. Automate transfers to make saving effortless, and consider high-yield savings accounts for better returns on cash.`;
    }

    generateRetirementAdvice(context) {
        return `For retirement planning, aim to save 10-15% of your income. Maximize employer 401(k) matching first, then consider IRA contributions. The earlier you start, the more compound interest works in your favor.`;
    }

    generateEmergencyFundAdvice(context) {
        const monthsCovered = (context.emergencyFund || 0) / (context.monthlyExpenses || 1);
        if (monthsCovered >= 6) {
            return `Excellent emergency fund! You have ${monthsCovered.toFixed(1)} months covered. Consider investing excess emergency savings for better returns.`;
        } else {
            return `Build your emergency fund to 3-6 months of expenses. You currently have ${monthsCovered.toFixed(1)} months covered. Start with $1,000, then gradually build up.`;
        }
    }

    generateGeneralAdvice(context) {
        return `Based on your financial profile, focus on: 1) Building/maintaining your emergency fund, 2) Maximizing employer 401(k) match, 3) Paying off high-interest debt, 4) Investing in diversified index funds. What specific area would you like to explore?`;
    }

    generateFallbackInsights(context) {
        const insights = [];
        const savingsRate = context.savingsRate || 0;
        const monthsCovered = (context.emergencyFund || 0) / (context.monthlyExpenses || 1);
        const netWorth = context.netWorth || 0;
        const totalInvestments = context.totalInvestments || 0;

        // Emergency fund insight
        if (monthsCovered < 3) {
            insights.push({
                type: 'emergency_fund',
                title: 'Build Your Emergency Fund',
                description: `Your emergency fund covers ${monthsCovered.toFixed(1)} months of expenses. Aim for 3-6 months ($${(context.monthlyExpenses * 3).toLocaleString()} - $${(context.monthlyExpenses * 6).toLocaleString()}) to protect against unexpected events.`,
                actionItems: ['Set up automatic transfers to savings', 'Consider a high-yield savings account', 'Start with $1,000 if starting from zero'],
                priority: monthsCovered < 1 ? 5 : 3
            });
        } else if (monthsCovered > 12) {
            insights.push({
                type: 'emergency_fund',
                title: 'Optimize Excess Emergency Savings',
                description: `You have ${monthsCovered.toFixed(1)} months of expenses saved. Consider investing excess beyond 6 months for better returns.`,
                actionItems: ['Move excess to investment accounts', 'Consider I-bonds for inflation protection', 'Maintain 6 months in high-yield savings'],
                priority: 2
            });
        }

        // Savings rate insight
        if (savingsRate < 20) {
            const targetSavings = context.monthlyIncome * 0.2;
            const currentSavings = context.monthlyIncome - context.monthlyExpenses;
            const gap = targetSavings - currentSavings;

            insights.push({
                type: 'savings_rate',
                title: 'Increase Your Savings Rate',
                description: `Your current savings rate is ${savingsRate.toFixed(1)}%. To reach the recommended 20%, you need to save an additional $${gap.toFixed(0)}/month.`,
                actionItems: [`Reduce expenses by $${gap.toFixed(0)}/month`, 'Track spending for 30 days', 'Focus on big three: housing, transportation, food'],
                priority: savingsRate < 10 ? 4 : 3
            });
        } else if (savingsRate > 50) {
            insights.push({
                type: 'savings_optimization',
                title: 'Optimize Your High Savings Rate',
                description: `Excellent ${savingsRate.toFixed(1)}% savings rate! Focus on tax optimization and investment allocation.`,
                actionItems: ['Max out 401(k) and IRA contributions', 'Consider tax-loss harvesting', 'Review investment allocation'],
                priority: 2
            });
        }

        // Investment allocation insight
        const investmentRatio = totalInvestments / netWorth;
        if (netWorth > 50000 && investmentRatio < 0.6) {
            insights.push({
                type: 'investment_allocation',
                title: 'Increase Investment Allocation',
                description: `Only ${(investmentRatio * 100).toFixed(1)}% of your net worth is invested. Consider increasing for long-term growth.`,
                actionItems: ['Review asset allocation', 'Consider low-cost index funds', 'Automate investment contributions'],
                priority: 3
            });
        }

        // Goal progress insight
        if (context.goals && context.goals.length > 0) {
            const behindGoals = context.goals.filter(g => {
                const progress = (g.current_amount / g.target_amount) * 100;
                return progress < 50 && new Date(g.target_date) < new Date(Date.now() + 365 * 24 * 60 * 60 * 1000);
            });

            if (behindGoals.length > 0) {
                insights.push({
                    type: 'goal_progress',
                    title: 'Accelerate Goal Progress',
                    description: `You have ${behindGoals.length} goal(s) that may need attention: ${behindGoals.map(g => g.name).join(', ')}.`,
                    actionItems: ['Review goal timelines', 'Increase monthly contributions', 'Consider side income'],
                    priority: 3
                });
            }
        }

        return { insights: insights.slice(0, 3) }; // Return top 3 insights
    }

    generateFallbackScenarioAnalysis(scenario, context) {
        return {
            impact: 'neutral',
            immediateEffects: 'This scenario would require careful financial planning and adjustment to your current budget.',
            longTermImplications: 'Long-term success depends on how well you prepare and adapt your financial strategy.',
            recommendations: [
                'Review your emergency fund adequacy',
                'Consider adjusting your budget',
                'Explore additional income sources if needed'
            ],
            riskMitigation: [
                'Maintain adequate emergency savings',
                'Diversify income sources',
                'Regular financial plan reviews'
            ]
        };
    }
}

module.exports = new AIFinancialCoach();
