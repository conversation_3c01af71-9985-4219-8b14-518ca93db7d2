const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const bcrypt = require('bcryptjs');

const DB_PATH = path.join(__dirname, 'wealthwise.db');

class Database {
    constructor() {
        this.db = null;
        this.init();
    }

    init() {
        this.db = new sqlite3.Database(DB_PATH, (err) => {
            if (err) {
                console.error('Error opening database:', err.message);
            } else {
                console.log('📁 Connected to SQLite database');
                this.createTables();
            }
        });
    }

    createTables() {
        const tables = [
            // Users table
            `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                plan_type TEXT DEFAULT 'basic',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Financial accounts table
            `CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                account_type TEXT NOT NULL,
                account_name TEXT NOT NULL,
                institution TEXT,
                balance DECIMAL(15,2) DEFAULT 0,
                currency TEXT DEFAULT 'USD',
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

            // Transactions table
            `CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                account_id INTEGER,
                amount DECIMAL(15,2) NOT NULL,
                description TEXT NOT NULL,
                category TEXT,
                transaction_date DATE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )`,

            // Goals table
            `CREATE TABLE IF NOT EXISTS goals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                target_amount DECIMAL(15,2) NOT NULL,
                current_amount DECIMAL(15,2) DEFAULT 0,
                target_date DATE,
                category TEXT,
                status TEXT DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

            // AI chat history table
            `CREATE TABLE IF NOT EXISTS chat_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                message TEXT NOT NULL,
                response TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

            // User preferences table
            `CREATE TABLE IF NOT EXISTS user_preferences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                preference_key TEXT NOT NULL,
                preference_value TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE(user_id, preference_key)
            )`,

            // Financial insights table
            `CREATE TABLE IF NOT EXISTS insights (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                insight_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                action_items TEXT,
                priority INTEGER DEFAULT 1,
                is_read BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

            // Contact requests table
            `CREATE TABLE IF NOT EXISTS contact_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT NOT NULL,
                company TEXT NOT NULL,
                phone TEXT,
                company_size TEXT,
                message TEXT NOT NULL,
                consent INTEGER DEFAULT 0,
                status TEXT DEFAULT 'new',
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        tables.forEach((tableSQL, index) => {
            this.db.run(tableSQL, (err) => {
                if (err) {
                    console.error(`Error creating table ${index + 1}:`, err.message);
                } else {
                    console.log(`✅ Table ${index + 1} created/verified`);
                }
            });
        });

        // Create indexes for better performance
        this.createIndexes();
        
        // Insert sample data
        this.insertSampleData();
    }

    createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
            'CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)',
            'CREATE INDEX IF NOT EXISTS idx_goals_user_id ON goals(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_chat_history_user_id ON chat_history(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_insights_user_id ON insights(user_id)'
        ];

        indexes.forEach(indexSQL => {
            this.db.run(indexSQL, (err) => {
                if (err && !err.message.includes('already exists')) {
                    console.error('Error creating index:', err.message);
                }
            });
        });
    }

    async insertSampleData() {
        // Check if sample user already exists
        this.db.get('SELECT id FROM users WHERE email = ?', ['<EMAIL>'], async (err, row) => {
            if (err) {
                console.error('Error checking for sample user:', err.message);
                return;
            }

            if (!row) {
                // Create sample user
                const hashedPassword = await bcrypt.hash('demo123', 10);
                
                this.db.run(
                    'INSERT INTO users (name, email, password_hash, plan_type) VALUES (?, ?, ?, ?)',
                    ['Demo User', '<EMAIL>', hashedPassword, 'pro'],
                    function(err) {
                        if (err) {
                            console.error('Error creating sample user:', err.message);
                            return;
                        }

                        const userId = this.lastID;
                        console.log('📝 Sample user created with ID:', userId);

                        // Insert sample accounts
                        const accounts = [
                            ['checking', 'Main Checking', 'Chase Bank', 15000],
                            ['savings', 'High Yield Savings', 'Ally Bank', 25000],
                            ['investment', 'Investment Account', 'Vanguard', 150000],
                            ['credit', 'Credit Card', 'Chase Sapphire', -2500]
                        ];

                        accounts.forEach(([type, name, institution, balance]) => {
                            database.db.run(
                                'INSERT INTO accounts (user_id, account_type, account_name, institution, balance) VALUES (?, ?, ?, ?, ?)',
                                [userId, type, name, institution, balance]
                            );
                        });

                        // Insert sample goals
                        const goals = [
                            ['Emergency Fund', 25000, 25000, '2023-12-01', 'emergency'],
                            ['House Down Payment', 100000, 65000, '2025-12-01', 'house'],
                            ['Retirement Fund', 1000000, 350000, '2055-01-01', 'retirement']
                        ];

                        goals.forEach(([name, target, current, targetDate, category]) => {
                            database.db.run(
                                'INSERT INTO goals (user_id, name, target_amount, current_amount, target_date, category) VALUES (?, ?, ?, ?, ?, ?)',
                                [userId, name, target, current, targetDate, category]
                            );
                        });

                        // Insert sample transactions
                        const transactions = [
                            [-5.47, 'Starbucks', 'Food & Dining', '2024-01-15'],
                            [-45.20, 'Shell Gas Station', 'Transportation', '2024-01-14'],
                            [4250.00, 'Salary Deposit', 'Income', '2024-01-01'],
                            [-1200.00, 'Rent Payment', 'Housing', '2024-01-01'],
                            [-150.00, 'Grocery Store', 'Food & Dining', '2024-01-13'],
                            [-80.00, 'Electric Bill', 'Utilities', '2024-01-12']
                        ];

                        transactions.forEach(([amount, description, category, date]) => {
                            database.db.run(
                                'INSERT INTO transactions (user_id, amount, description, category, transaction_date) VALUES (?, ?, ?, ?, ?)',
                                [userId, amount, description, category, date]
                            );
                        });

                        console.log('✅ Sample data inserted successfully');
                    }
                );
            }
        });
    }

    // User methods
    createUser(userData, callback) {
        const { name, email, password } = userData;

        if (callback) {
            bcrypt.hash(password, 10, (err, hash) => {
                if (err) {
                    return callback(err);
                }

                this.db.run(
                    'INSERT INTO users (name, email, password_hash) VALUES (?, ?, ?)',
                    [name, email, hash],
                    function(err) {
                        if (err) {
                            return callback(err);
                        }
                        callback(null, { id: this.lastID, name, email });
                    }
                );
            });
        } else {
            return new Promise(async (resolve, reject) => {
                try {
                    const hash = await bcrypt.hash(password, 10);
                    this.db.run(
                        'INSERT INTO users (name, email, password_hash) VALUES (?, ?, ?)',
                        [name, email, hash],
                        function(err) {
                            if (err) {
                                return reject(err);
                            }
                            resolve({ id: this.lastID, name, email });
                        }
                    );
                } catch (err) {
                    reject(err);
                }
            });
        }
    }

    getUserByEmail(email, callback) {
        if (callback) {
            this.db.get(
                'SELECT * FROM users WHERE email = ?',
                [email],
                callback
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.get(
                    'SELECT * FROM users WHERE email = ?',
                    [email],
                    (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    }
                );
            });
        }
    }

    getUserById(id, callback) {
        if (callback) {
            this.db.get(
                'SELECT id, name, email, plan_type, created_at FROM users WHERE id = ?',
                [id],
                callback
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.get(
                    'SELECT id, name, email, plan_type, created_at FROM users WHERE id = ?',
                    [id],
                    (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    }
                );
            });
        }
    }

    // Account methods
    getUserAccounts(userId, callback) {
        if (callback) {
            this.db.all(
                'SELECT * FROM accounts WHERE user_id = ? AND is_active = 1 ORDER BY account_type',
                [userId],
                callback
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.all(
                    'SELECT * FROM accounts WHERE user_id = ? AND is_active = 1 ORDER BY account_type',
                    [userId],
                    (err, rows) => {
                        if (err) reject(err);
                        else resolve(rows || []);
                    }
                );
            });
        }
    }

    // Transaction methods
    getUserTransactions(userId, limit = 50, callback) {
        if (callback) {
            this.db.all(
                'SELECT * FROM transactions WHERE user_id = ? ORDER BY transaction_date DESC, created_at DESC LIMIT ?',
                [userId, limit],
                callback
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.all(
                    'SELECT * FROM transactions WHERE user_id = ? ORDER BY transaction_date DESC, created_at DESC LIMIT ?',
                    [userId, limit],
                    (err, rows) => {
                        if (err) reject(err);
                        else resolve(rows || []);
                    }
                );
            });
        }
    }

    addTransaction(transactionData, callback) {
        const { userId, accountId, amount, description, category, transactionDate } = transactionData;
        
        this.db.run(
            'INSERT INTO transactions (user_id, account_id, amount, description, category, transaction_date) VALUES (?, ?, ?, ?, ?, ?)',
            [userId, accountId, amount, description, category, transactionDate],
            function(err) {
                if (err) {
                    return callback(err);
                }
                callback(null, { id: this.lastID });
            }
        );
    }

    // Goal methods
    getUserGoals(userId, callback) {
        if (callback) {
            this.db.all(
                'SELECT * FROM goals WHERE user_id = ? ORDER BY created_at DESC',
                [userId],
                callback
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.all(
                    'SELECT * FROM goals WHERE user_id = ? ORDER BY created_at DESC',
                    [userId],
                    (err, rows) => {
                        if (err) reject(err);
                        else resolve(rows || []);
                    }
                );
            });
        }
    }

    createGoal(goalData, callback) {
        const { userId, name, targetAmount, targetDate, category } = goalData;
        
        this.db.run(
            'INSERT INTO goals (user_id, name, target_amount, target_date, category) VALUES (?, ?, ?, ?, ?)',
            [userId, name, targetAmount, targetDate, category],
            function(err) {
                if (err) {
                    return callback(err);
                }
                callback(null, { id: this.lastID });
            }
        );
    }

    updateGoalProgress(goalId, currentAmount, callback) {
        this.db.run(
            'UPDATE goals SET current_amount = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [currentAmount, goalId],
            callback
        );
    }

    // Chat history methods
    saveChatMessage(userId, message, response, callback) {
        if (callback) {
            this.db.run(
                'INSERT INTO chat_history (user_id, message, response) VALUES (?, ?, ?)',
                [userId, message, response],
                function(err) {
                    if (err) {
                        return callback(err);
                    }
                    callback(null, { id: this.lastID });
                }
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.run(
                    'INSERT INTO chat_history (user_id, message, response) VALUES (?, ?, ?)',
                    [userId, message, response],
                    function(err) {
                        if (err) {
                            return reject(err);
                        }
                        resolve({ id: this.lastID });
                    }
                );
            });
        }
    }

    getChatHistory(userId, limit = 50, callback) {
        if (callback) {
            this.db.all(
                'SELECT * FROM chat_history WHERE user_id = ? ORDER BY created_at DESC LIMIT ?',
                [userId, limit],
                callback
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.all(
                    'SELECT * FROM chat_history WHERE user_id = ? ORDER BY created_at DESC LIMIT ?',
                    [userId, limit],
                    (err, rows) => {
                        if (err) reject(err);
                        else resolve(rows || []);
                    }
                );
            });
        }
    }

    // Insights methods
    createInsight(insightData, callback) {
        const { user_id, insight_type, title, description, action_items, priority } = insightData;

        if (callback) {
            this.db.run(
                'INSERT INTO insights (user_id, insight_type, title, description, action_items, priority) VALUES (?, ?, ?, ?, ?, ?)',
                [user_id, insight_type, title, description, JSON.stringify(action_items), priority],
                function(err) {
                    if (err) {
                        return callback(err);
                    }
                    callback(null, { id: this.lastID, ...insightData });
                }
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.run(
                    'INSERT INTO insights (user_id, insight_type, title, description, action_items, priority) VALUES (?, ?, ?, ?, ?, ?)',
                    [user_id, insight_type, title, description, JSON.stringify(action_items), priority],
                    function(err) {
                        if (err) {
                            return reject(err);
                        }
                        resolve({ id: this.lastID, ...insightData });
                    }
                );
            });
        }
    }

    // Contact request methods
    saveContactRequest(contactData, callback) {
        const { name, email, company, phone, company_size, message, consent, status, created_at } = contactData;

        if (callback) {
            this.db.run(
                'INSERT INTO contact_requests (name, email, company, phone, company_size, message, consent, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                [name, email, company, phone, company_size, message, consent, status, created_at],
                function(err) {
                    if (err) {
                        return callback(err);
                    }
                    callback(null, { id: this.lastID, ...contactData });
                }
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.run(
                    'INSERT INTO contact_requests (name, email, company, phone, company_size, message, consent, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                    [name, email, company, phone, company_size, message, consent, status, created_at],
                    function(err) {
                        if (err) {
                            return reject(err);
                        }
                        resolve({ id: this.lastID, ...contactData });
                    }
                );
            });
        }
    }

    getContactRequests(callback) {
        if (callback) {
            this.db.all(
                'SELECT * FROM contact_requests ORDER BY created_at DESC',
                [],
                callback
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.all(
                    'SELECT * FROM contact_requests ORDER BY created_at DESC',
                    [],
                    (err, rows) => {
                        if (err) reject(err);
                        else resolve(rows || []);
                    }
                );
            });
        }
    }

    updateContactRequest(id, updateData, callback) {
        const { status, notes } = updateData;

        if (callback) {
            this.db.run(
                'UPDATE contact_requests SET status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [status, notes, id],
                function(err) {
                    if (err) {
                        return callback(err);
                    }
                    callback(null, { id, status, notes });
                }
            );
        } else {
            return new Promise((resolve, reject) => {
                this.db.run(
                    'UPDATE contact_requests SET status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [status, notes, id],
                    function(err) {
                        if (err) {
                            return reject(err);
                        }
                        resolve({ id, status, notes });
                    }
                );
            });
        }
    }

    // Close database connection
    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('Error closing database:', err.message);
                } else {
                    console.log('📁 Database connection closed');
                }
            });
        }
    }
}

// Create and export database instance
const database = new Database();

module.exports = database;
