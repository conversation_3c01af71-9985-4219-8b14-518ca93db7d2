// Direct API test to verify endpoints are working
const http = require('http');

async function testAPIDirectly() {
    console.log('🔧 Testing WealthWise AI API Endpoints Directly...\n');

    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    await testEndpoint('GET', '/api/health', null);

    // Test 2: Signup
    console.log('\n2. Testing Signup...');
    const signupData = {
        name: 'API Test User',
        email: 'apitest' + Date.now() + '@example.com',
        password: 'testpass123'
    };
    await testEndpoint('POST', '/api/auth/signup', signupData);

    // Test 3: Login
    console.log('\n3. Testing Login...');
    const loginData = {
        email: '<EMAIL>',
        password: 'demo123'
    };
    await testEndpoint('POST', '/api/auth/login', loginData);

    // Test 4: Contact Sales
    console.log('\n4. Testing Contact Sales...');
    const contactData = {
        name: 'API Test Contact',
        email: '<EMAIL>',
        company: 'Test Company',
        message: 'API test message',
        consent: true
    };
    await testEndpoint('POST', '/api/contact/sales', contactData);

    console.log('\n🎯 API Test Complete!');
}

function testEndpoint(method, path, data) {
    return new Promise((resolve) => {
        const postData = data ? JSON.stringify(data) : null;
        
        const options = {
            hostname: 'localhost',
            port: 3002,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        if (postData) {
            options.headers['Content-Length'] = Buffer.byteLength(postData);
        }

        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(responseData);
                    
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        console.log(`✅ ${method} ${path}: SUCCESS`);
                        console.log(`   Status: ${res.statusCode}`);
                        console.log(`   Response:`, jsonData);
                    } else {
                        console.log(`❌ ${method} ${path}: FAILED`);
                        console.log(`   Status: ${res.statusCode}`);
                        console.log(`   Error:`, jsonData);
                    }
                } catch (error) {
                    console.log(`❌ ${method} ${path}: INVALID JSON`);
                    console.log(`   Status: ${res.statusCode}`);
                    console.log(`   Raw Response:`, responseData);
                }
                resolve();
            });
        });

        req.on('error', (error) => {
            console.log(`❌ ${method} ${path}: CONNECTION ERROR`);
            console.log(`   Error: ${error.message}`);
            console.log(`   This usually means:`);
            console.log(`     - Server is not running`);
            console.log(`     - Wrong port (should be 3002)`);
            console.log(`     - Firewall blocking connection`);
            resolve();
        });

        if (postData) {
            req.write(postData);
        }
        
        req.end();
    });
}

// Run the test
testAPIDirectly().catch(console.error);
