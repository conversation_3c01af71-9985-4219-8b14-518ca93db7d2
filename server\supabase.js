const bcrypt = require('bcryptjs');

// For now, we'll use a simplified database interface that works with the Management API
// In production, you would use the actual Supabase client with proper API keys

class SupabaseManagementAPI {
    constructor() {
        this.projectId = 'hkvhlqyyxjwnhzldxnao';
        this.baseUrl = `https://hkvhlqyyxjwnhzldxnao.supabase.co`;
    }

    async query(sql, params = []) {
        // This is a placeholder for the actual Supabase client
        // In a real implementation, you would use the Supabase client here
        console.log('Executing query:', sql);
        return { data: [], error: null };
    }
}

class SupabaseDatabase {
    constructor() {
        this.api = new SupabaseManagementAPI();
        console.log('🚀 Connected to Supabase database');
        // Sample data is already created via Management API
        console.log('✅ Sample data available in Supabase');
    }

    async initializeSampleData() {
        try {
            // Check if sample user already exists
            const { data: existingUser } = await this.client
                .from('users')
                .select('id')
                .eq('email', '<EMAIL>')
                .single();

            if (!existingUser) {
                console.log('📝 Creating sample data...');
                await this.createSampleUser();
            } else {
                console.log('✅ Sample data already exists');
            }
        } catch (error) {
            console.error('Error initializing sample data:', error);
        }
    }

    async createSampleUser() {
        try {
            // Create sample user
            const hashedPassword = await bcrypt.hash('demo123', 10);
            
            const { data: user, error: userError } = await this.client
                .from('users')
                .insert({
                    name: 'Demo User',
                    email: '<EMAIL>',
                    password_hash: hashedPassword,
                    plan_type: 'pro'
                })
                .select()
                .single();

            if (userError) throw userError;

            console.log('👤 Sample user created:', user.id);

            // Create sample accounts
            const accounts = [
                {
                    user_id: user.id,
                    account_type: 'checking',
                    account_name: 'Main Checking',
                    institution: 'Chase Bank',
                    balance: 15000
                },
                {
                    user_id: user.id,
                    account_type: 'savings',
                    account_name: 'High Yield Savings',
                    institution: 'Ally Bank',
                    balance: 25000
                },
                {
                    user_id: user.id,
                    account_type: 'investment',
                    account_name: 'Investment Account',
                    institution: 'Vanguard',
                    balance: 150000
                },
                {
                    user_id: user.id,
                    account_type: 'credit',
                    account_name: 'Credit Card',
                    institution: 'Chase Sapphire',
                    balance: -2500
                }
            ];

            const { error: accountsError } = await this.client
                .from('accounts')
                .insert(accounts);

            if (accountsError) throw accountsError;

            // Create sample goals
            const goals = [
                {
                    user_id: user.id,
                    name: 'Emergency Fund',
                    target_amount: 25000,
                    current_amount: 25000,
                    target_date: '2023-12-01',
                    category: 'emergency',
                    status: 'completed'
                },
                {
                    user_id: user.id,
                    name: 'House Down Payment',
                    target_amount: 100000,
                    current_amount: 65000,
                    target_date: '2025-12-01',
                    category: 'house'
                },
                {
                    user_id: user.id,
                    name: 'Retirement Fund',
                    target_amount: 1000000,
                    current_amount: 350000,
                    target_date: '2055-01-01',
                    category: 'retirement'
                }
            ];

            const { error: goalsError } = await this.client
                .from('goals')
                .insert(goals);

            if (goalsError) throw goalsError;

            // Create sample transactions
            const transactions = [
                {
                    user_id: user.id,
                    amount: -5.47,
                    description: 'Starbucks',
                    category: 'Food & Dining',
                    transaction_date: '2024-01-15'
                },
                {
                    user_id: user.id,
                    amount: -45.20,
                    description: 'Shell Gas Station',
                    category: 'Transportation',
                    transaction_date: '2024-01-14'
                },
                {
                    user_id: user.id,
                    amount: 4250.00,
                    description: 'Salary Deposit',
                    category: 'Income',
                    transaction_date: '2024-01-01'
                },
                {
                    user_id: user.id,
                    amount: -1200.00,
                    description: 'Rent Payment',
                    category: 'Housing',
                    transaction_date: '2024-01-01'
                },
                {
                    user_id: user.id,
                    amount: -150.00,
                    description: 'Grocery Store',
                    category: 'Food & Dining',
                    transaction_date: '2024-01-13'
                },
                {
                    user_id: user.id,
                    amount: -80.00,
                    description: 'Electric Bill',
                    category: 'Utilities',
                    transaction_date: '2024-01-12'
                }
            ];

            const { error: transactionsError } = await this.client
                .from('transactions')
                .insert(transactions);

            if (transactionsError) throw transactionsError;

            console.log('✅ Sample data created successfully');
        } catch (error) {
            console.error('Error creating sample data:', error);
        }
    }

    // User methods
    async createUser(userData) {
        const { name, email, password } = userData;
        
        try {
            const hashedPassword = await bcrypt.hash(password, 10);
            
            const { data, error } = await this.client
                .from('users')
                .insert({
                    name,
                    email,
                    password_hash: hashedPassword
                })
                .select()
                .single();

            if (error) throw error;
            
            return { id: data.id, name: data.name, email: data.email };
        } catch (error) {
            throw error;
        }
    }

    async getUserByEmail(email) {
        try {
            const { data, error } = await this.client
                .from('users')
                .select('*')
                .eq('email', email)
                .single();

            if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
            
            return data;
        } catch (error) {
            throw error;
        }
    }

    async getUserById(id) {
        try {
            const { data, error } = await this.client
                .from('users')
                .select('id, name, email, plan_type, created_at')
                .eq('id', id)
                .single();

            if (error && error.code !== 'PGRST116') throw error;
            
            return data;
        } catch (error) {
            throw error;
        }
    }

    // Account methods
    async getUserAccounts(userId) {
        try {
            const { data, error } = await this.client
                .from('accounts')
                .select('*')
                .eq('user_id', userId)
                .eq('is_active', true)
                .order('account_type');

            if (error) throw error;
            
            return data || [];
        } catch (error) {
            throw error;
        }
    }

    async createAccount(accountData) {
        try {
            const { data, error } = await this.client
                .from('accounts')
                .insert(accountData)
                .select()
                .single();

            if (error) throw error;
            
            return data;
        } catch (error) {
            throw error;
        }
    }

    // Transaction methods
    async getUserTransactions(userId, limit = 50) {
        try {
            const { data, error } = await this.client
                .from('transactions')
                .select('*')
                .eq('user_id', userId)
                .order('transaction_date', { ascending: false })
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) throw error;
            
            return data || [];
        } catch (error) {
            throw error;
        }
    }

    async addTransaction(transactionData) {
        try {
            const { data, error } = await this.client
                .from('transactions')
                .insert(transactionData)
                .select()
                .single();

            if (error) throw error;
            
            return data;
        } catch (error) {
            throw error;
        }
    }

    // Goal methods
    async getUserGoals(userId) {
        try {
            const { data, error } = await this.client
                .from('goals')
                .select('*')
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (error) throw error;
            
            return data || [];
        } catch (error) {
            throw error;
        }
    }

    async createGoal(goalData) {
        try {
            const { data, error } = await this.client
                .from('goals')
                .insert(goalData)
                .select()
                .single();

            if (error) throw error;
            
            return data;
        } catch (error) {
            throw error;
        }
    }

    async updateGoalProgress(goalId, currentAmount) {
        try {
            const { data, error } = await this.client
                .from('goals')
                .update({ current_amount: currentAmount })
                .eq('id', goalId)
                .select()
                .single();

            if (error) throw error;
            
            return data;
        } catch (error) {
            throw error;
        }
    }

    // Chat history methods
    async saveChatMessage(userId, message, response) {
        try {
            const { data, error } = await this.client
                .from('chat_history')
                .insert({
                    user_id: userId,
                    message,
                    response
                })
                .select()
                .single();

            if (error) throw error;
            
            return data;
        } catch (error) {
            throw error;
        }
    }

    async getChatHistory(userId, limit = 50) {
        try {
            const { data, error } = await this.client
                .from('chat_history')
                .select('*')
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) throw error;
            
            return data || [];
        } catch (error) {
            throw error;
        }
    }

    // Insights methods
    async createInsight(insightData) {
        try {
            const { data, error } = await this.client
                .from('insights')
                .insert(insightData)
                .select()
                .single();

            if (error) throw error;
            
            return data;
        } catch (error) {
            throw error;
        }
    }

    async getUserInsights(userId) {
        try {
            const { data, error } = await this.client
                .from('insights')
                .select('*')
                .eq('user_id', userId)
                .order('priority', { ascending: false })
                .order('created_at', { ascending: false });

            if (error) throw error;
            
            return data || [];
        } catch (error) {
            throw error;
        }
    }

    // Utility method to execute raw SQL (for complex queries)
    async executeQuery(query, params = []) {
        try {
            const { data, error } = await this.client.rpc('execute_sql', {
                query_text: query,
                query_params: params
            });

            if (error) throw error;
            
            return data;
        } catch (error) {
            throw error;
        }
    }
}

// Create and export database instance
const database = new SupabaseDatabase();

module.exports = database;
