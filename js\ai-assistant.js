// AI Assistant Chat Functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeAIAssistant();
});

let chatHistory = [];
let isTyping = false;

function initializeAIAssistant() {
    const chatInput = document.getElementById('chatInput');
    const sendButton = document.getElementById('sendButton');
    
    if (chatInput) {
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    }
    
    if (sendButton) {
        sendButton.addEventListener('click', sendMessage);
    }
    
    // Load chat history from localStorage
    loadChatHistory();
}

function loadChatHistory() {
    const savedHistory = localStorage.getItem('wealthwise_chat_history');
    if (savedHistory) {
        chatHistory = JSON.parse(savedHistory);
        displayChatHistory();
    }
}

function saveChatHistory() {
    localStorage.setItem('wealthwise_chat_history', JSON.stringify(chatHistory));
}

function displayChatHistory() {
    const chatMessages = document.getElementById('chatMessages');
    if (!chatMessages) return;
    
    // Clear existing messages except the initial AI greeting
    const greeting = chatMessages.querySelector('.ai-message');
    chatMessages.innerHTML = '';
    if (greeting) {
        chatMessages.appendChild(greeting);
    }
    
    // Display chat history
    chatHistory.forEach(message => {
        addMessageToChat(message.content, message.type, false);
    });
    
    scrollToBottom();
}

async function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const message = chatInput.value.trim();
    
    if (!message || isTyping) return;
    
    // Add user message to chat
    addMessageToChat(message, 'user');
    chatHistory.push({ content: message, type: 'user', timestamp: new Date() });
    
    // Clear input
    chatInput.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        // Get AI response
        const response = await getAIResponse(message);
        
        // Remove typing indicator
        hideTypingIndicator();
        
        // Add AI response to chat
        addMessageToChat(response, 'ai');
        chatHistory.push({ content: response, type: 'ai', timestamp: new Date() });
        
        // Save chat history
        saveChatHistory();
        
    } catch (error) {
        hideTypingIndicator();
        addMessageToChat('Sorry, I encountered an error. Please try again.', 'ai');
    }
}

function addMessageToChat(content, type, shouldScroll = true) {
    const chatMessages = document.getElementById('chatMessages');
    if (!chatMessages) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}-message`;
    
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = type === 'ai' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = `<p>${content}</p>`;
    
    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);
    
    chatMessages.appendChild(messageDiv);
    
    if (shouldScroll) {
        scrollToBottom();
    }
}

function showTypingIndicator() {
    isTyping = true;
    const chatMessages = document.getElementById('chatMessages');
    
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message ai-message typing-indicator';
    typingDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    scrollToBottom();
}

function hideTypingIndicator() {
    isTyping = false;
    const typingIndicator = document.querySelector('.typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

async function getAIResponse(userMessage) {
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // Get financial context
    const financialContext = getFinancialContext();
    
    // Generate contextual response based on user message
    return generateContextualResponse(userMessage, financialContext);
}

function getFinancialContext() {
    // Get user's financial data for context
    return {
        netWorth: 247850,
        monthlyIncome: 8500,
        monthlyExpenses: 4250,
        savingsRate: 50,
        emergencyFund: 25000,
        investments: 150000,
        debt: 0,
        age: 32,
        goals: [
            { name: 'House Down Payment', target: 100000, current: 65000 },
            { name: 'Retirement', target: 1000000, current: 350000 }
        ]
    };
}

function generateContextualResponse(message, context) {
    const lowerMessage = message.toLowerCase();
    
    // Budget-related responses
    if (lowerMessage.includes('budget') || lowerMessage.includes('spending')) {
        return generateBudgetAdvice(context);
    }
    
    // Investment-related responses
    if (lowerMessage.includes('invest') || lowerMessage.includes('portfolio') || lowerMessage.includes('stock')) {
        return generateInvestmentAdvice(context);
    }
    
    // Savings-related responses
    if (lowerMessage.includes('save') || lowerMessage.includes('saving')) {
        return generateSavingsAdvice(context);
    }
    
    // Retirement-related responses
    if (lowerMessage.includes('retire') || lowerMessage.includes('retirement')) {
        return generateRetirementAdvice(context);
    }
    
    // Tax-related responses
    if (lowerMessage.includes('tax') || lowerMessage.includes('taxes')) {
        return generateTaxAdvice(context);
    }
    
    // Goal-related responses
    if (lowerMessage.includes('goal') || lowerMessage.includes('house')) {
        return generateGoalAdvice(context);
    }
    
    // Emergency fund responses
    if (lowerMessage.includes('emergency')) {
        return generateEmergencyFundAdvice(context);
    }
    
    // General financial advice
    return generateGeneralAdvice(context);
}

function generateBudgetAdvice(context) {
    const responses = [
        `Great question about budgeting! With your current income of $${context.monthlyIncome.toLocaleString()} and expenses of $${context.monthlyExpenses.toLocaleString()}, you're saving ${context.savingsRate}% of your income - that's excellent! Consider the 50/30/20 rule: 50% for needs, 30% for wants, and 20% for savings and debt repayment.`,
        
        `Your current spending pattern shows you're doing well with a ${context.savingsRate}% savings rate. To optimize further, try tracking your expenses by category for a month. I notice you could potentially save more on dining out based on your transaction history.`,
        
        `Based on your financial profile, you're already budgeting effectively. Consider automating your savings to make it even easier. With your current income, you could potentially increase your savings rate to 55-60% if you optimize discretionary spending.`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
}

function generateInvestmentAdvice(context) {
    const responses = [
        `With your current investment portfolio of $${context.investments.toLocaleString()} and your age of ${context.age}, you're in a good position for long-term growth. Consider a diversified portfolio with 70-80% stocks and 20-30% bonds. Your emergency fund is solid, so you can take on appropriate risk.`,
        
        `Your investment strategy should align with your goals. For your retirement goal of $1M, you're currently at $${context.goals[1].current.toLocaleString()}. With consistent monthly contributions and compound growth, you're on track. Consider low-cost index funds for broad market exposure.`,
        
        `Given your strong financial foundation and ${context.savingsRate}% savings rate, you could consider increasing your equity allocation. Dollar-cost averaging into diversified index funds would be a solid strategy for your time horizon.`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
}

function generateSavingsAdvice(context) {
    const responses = [
        `You're already saving ${context.savingsRate}% of your income - that's fantastic! The average American saves only 13%. With your emergency fund fully funded at $${context.emergencyFund.toLocaleString()}, focus on investing additional savings for long-term growth.`,
        
        `Your savings discipline is impressive! Consider opening a high-yield savings account for your emergency fund to earn more interest. For additional savings beyond your emergency fund, investing in tax-advantaged accounts like 401(k) or IRA would be beneficial.`,
        
        `With your current savings rate, you're building wealth effectively. Consider automating transfers to different savings goals - maybe $1,000/month toward your house down payment and the rest toward investments.`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
}

function generateRetirementAdvice(context) {
    const currentRetirement = context.goals.find(g => g.name === 'Retirement');
    const yearsToRetirement = 65 - context.age;
    
    return `You're ${context.age} with $${currentRetirement.current.toLocaleString()} saved for retirement. To reach your $1M goal by 65, you need about $${Math.round((currentRetirement.target - currentRetirement.current) / yearsToRetirement / 12).toLocaleString()}/month in contributions, assuming 7% annual returns. You're actually ahead of schedule! Consider maximizing your 401(k) match and IRA contributions.`;
}

function generateTaxAdvice(context) {
    const responses = [
        `With your income level, consider maximizing tax-advantaged accounts. You can contribute $22,500 to your 401(k) and $6,500 to an IRA annually. This could save you significant taxes while building wealth.`,
        
        `Tax optimization strategies for your situation: 1) Max out 401(k) contributions, 2) Consider a backdoor Roth IRA if income limits apply, 3) Use HSA if available - it's triple tax-advantaged, 4) Consider tax-loss harvesting in taxable accounts.`,
        
        `Based on your savings rate and income, you're likely in a higher tax bracket. Focus on pre-tax retirement contributions now, and consider Roth conversions in lower-income years or retirement.`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
}

function generateGoalAdvice(context) {
    const houseGoal = context.goals.find(g => g.name === 'House Down Payment');
    const progress = (houseGoal.current / houseGoal.target) * 100;
    
    return `You're ${progress.toFixed(1)}% of the way to your house down payment goal! With $${houseGoal.current.toLocaleString()} saved toward your $${houseGoal.target.toLocaleString()} target, you need $${(houseGoal.target - houseGoal.current).toLocaleString()} more. At your current savings rate, you could reach this goal in about ${Math.ceil((houseGoal.target - houseGoal.current) / (context.monthlyIncome * context.savingsRate / 100))} months.`;
}

function generateEmergencyFundAdvice(context) {
    const monthsCovered = context.emergencyFund / context.monthlyExpenses;
    
    return `Excellent work on your emergency fund! You have $${context.emergencyFund.toLocaleString()} saved, which covers ${monthsCovered.toFixed(1)} months of expenses. This exceeds the recommended 3-6 months. Since you're well-covered, consider investing any additional emergency savings for better returns.`;
}

function generateGeneralAdvice(context) {
    const responses = [
        `Your financial health looks strong! With a ${context.savingsRate}% savings rate, fully funded emergency fund, and clear goals, you're on an excellent path. Focus on consistent investing and staying disciplined with your spending plan.`,
        
        `You're doing many things right financially. Your net worth of $${context.netWorth.toLocaleString()} at age ${context.age} puts you ahead of most Americans. Keep focusing on your long-term goals and consider working with a fee-only financial advisor for advanced strategies.`,
        
        `Based on your financial profile, you have a solid foundation. Consider these next steps: 1) Optimize your investment allocation, 2) Look into tax-loss harvesting, 3) Review insurance coverage, 4) Consider estate planning basics like a will and beneficiaries.`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
}

function askQuickQuestion(type) {
    const questions = {
        budget: "Can you help me create a budget based on my current income and expenses?",
        invest: "What investment strategy would you recommend for my situation?",
        tax: "How can I optimize my taxes with my current income level?",
        retirement: "Am I on track for retirement? What should I focus on?"
    };
    
    const chatInput = document.getElementById('chatInput');
    if (chatInput && questions[type]) {
        chatInput.value = questions[type];
        sendMessage();
    }
}

function clearChat() {
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        // Keep only the initial AI greeting
        const greeting = chatMessages.querySelector('.ai-message');
        chatMessages.innerHTML = '';
        if (greeting) {
            chatMessages.appendChild(greeting);
        }
    }
    
    // Clear chat history
    chatHistory = [];
    localStorage.removeItem('wealthwise_chat_history');
}

// Add typing animation styles
const typingStyles = document.createElement('style');
typingStyles.textContent = `
    .typing-dots {
        display: flex;
        gap: 4px;
        padding: 8px 0;
    }
    
    .typing-dots span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: var(--text-secondary);
        animation: typing 1.4s infinite ease-in-out;
    }
    
    .typing-dots span:nth-child(1) {
        animation-delay: -0.32s;
    }
    
    .typing-dots span:nth-child(2) {
        animation-delay: -0.16s;
    }
    
    @keyframes typing {
        0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
        }
        40% {
            transform: scale(1);
            opacity: 1;
        }
    }
`;
document.head.appendChild(typingStyles);

// Make functions globally available
window.sendMessage = sendMessage;
window.askQuickQuestion = askQuickQuestion;
window.clearChat = clearChat;
