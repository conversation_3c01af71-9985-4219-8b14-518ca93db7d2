{"version": 3, "file": "files.js", "sourceRoot": "", "sources": ["../../src/resources/beta/files.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;AAEtF,qDAAkD;AAGlD,yDAA2E;AAE3E,uDAAsD;AAEtD,uDAAqE;AACrE,uDAAiD;AAEjD,MAAa,KAAM,SAAQ,sBAAW;IACpC;;;;;;;;;;OAUG;IACH,IAAI,CACF,SAA4C,EAAE,EAC9C,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,CAAA,iBAAkB,CAAA,EAAE;YAC9D,KAAK;YACL,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,sBAAsB,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC3E,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CACJ,MAAc,EACd,SAA8C,EAAE,EAChD,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAA,WAAI,EAAA,aAAa,MAAM,EAAE,EAAE;YACpD,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,sBAAsB,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC3E,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,QAAQ,CACN,MAAc,EACd,SAAgD,EAAE,EAClD,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAA,WAAI,EAAA,aAAa,MAAM,UAAU,EAAE;YACzD,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB;oBACE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,sBAAsB,CAAC,CAAC,QAAQ,EAAE;oBACvE,MAAM,EAAE,oBAAoB;iBAC7B;gBACD,OAAO,EAAE,OAAO;aACjB,CAAC;YACF,gBAAgB,EAAE,IAAI;SACvB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACH,gBAAgB,CACd,MAAc,EACd,SAAwD,EAAE,EAC1D,OAAwB;QAExB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAA,WAAI,EAAA,aAAa,MAAM,EAAE,EAAE;YACjD,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,sBAAsB,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC3E,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,MAAwB,EAAE,OAAwB;QACvD,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CACtB,WAAW,EACX,IAAA,qCAA2B,EACzB;YACE,IAAI;YACJ,GAAG,OAAO;YACV,OAAO,EAAE,IAAA,sBAAY,EAAC;gBACpB,EAAE,gBAAgB,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE,sBAAsB,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC3E,OAAO,EAAE,OAAO;aACjB,CAAC;SACH,EACD,IAAI,CAAC,OAAO,CACb,CACF,CAAC;IACJ,CAAC;CACF;AAvID,sBAuIC"}