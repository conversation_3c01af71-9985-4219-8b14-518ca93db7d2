{"version": 3, "file": "pagination.js", "sourceRoot": "", "sources": ["../src/core/pagination.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;;;AAEtF,sCAAyC;AAEzC,gDAAwE;AAExE,kDAA2C;AAE3C,wDAAoD;AAIpD,MAAsB,YAAY;IAOhC,YAAY,MAAqB,EAAE,QAAkB,EAAE,IAAa,EAAE,OAA4B;QANlG,uCAAuB;QAOrB,+BAAA,IAAI,wBAAW,MAAM,MAAA,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAMD,WAAW;QACT,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,MAAM;YAAE,OAAO,KAAK,CAAC;QAChC,OAAO,IAAI,CAAC,sBAAsB,EAAE,IAAI,IAAI,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAClD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,sBAAc,CACtB,uFAAuF,CACxF,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,+BAAA,IAAI,4BAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,WAAkB,EAAE,WAAW,CAAC,CAAC;IACjF,CAAC;IAED,KAAK,CAAC,CAAC,SAAS;QACd,IAAI,IAAI,GAAS,IAAI,CAAC;QACtB,MAAM,IAAI,CAAC;QACX,OAAO,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YAC1B,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,CAAC,wCAAC,MAAM,CAAC,aAAa,EAAC;QAC3B,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YAC1C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC;YACb,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAnDD,oCAmDC;AAED;;;;;;;;GAQG;AACH,MAAa,WAIX,SAAQ,wBAAqB;IAG7B,YACE,MAAqB,EACrB,OAAkC,EAClC,IAA4E;QAE5E,KAAK,CACH,MAAM,EACN,OAAO,EACP,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CACtB,IAAI,IAAI,CACN,MAAM,EACN,KAAK,CAAC,QAAQ,EACd,MAAM,IAAA,4BAAoB,EAAC,MAAM,EAAE,KAAK,CAAC,EACzC,KAAK,CAAC,OAAO,CACc,CAChC,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC;QACxB,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC;QACb,CAAC;IACH,CAAC;CACF;AAtCD,kCAsCC;AAuBD,MAAa,IAAW,SAAQ,YAAkB;IAShD,YACE,MAAqB,EACrB,QAAkB,EAClB,IAAwB,EACxB,OAA4B;QAE5B,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;QACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;IACtC,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;IACzB,CAAC;IAEQ,WAAW;QAClB,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC;IAED,sBAAsB;QACpB,IAAK,IAAI,CAAC,OAAO,CAAC,KAAiC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;YACnE,aAAa;YACb,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO;gBACL,GAAG,IAAI,CAAC,OAAO;gBACf,KAAK,EAAE;oBACL,GAAG,IAAA,iBAAQ,EAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;oBAC/B,SAAS,EAAE,QAAQ;iBACpB;aACF,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,GAAG,IAAI,CAAC,OAAO;YACf,KAAK,EAAE;gBACL,GAAG,IAAA,iBAAQ,EAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC/B,QAAQ,EAAE,MAAM;aACjB;SACF,CAAC;IACJ,CAAC;CACF;AAjED,oBAiEC"}