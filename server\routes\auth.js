const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// Use appropriate database based on configuration
const database = process.env.USE_SUPABASE === 'true'
    ? require('../supabase')
    : require('../database');

const router = express.Router();

// JWT secret (in production, use environment variable)
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Register new user
router.post('/signup', async (req, res) => {
    try {
        const { name, email, password } = req.body;

        // Validation
        if (!name || !email || !password) {
            return res.status(400).json({ 
                message: 'Name, email, and password are required' 
            });
        }

        if (password.length < 6) {
            return res.status(400).json({ 
                message: 'Password must be at least 6 characters long' 
            });
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({ 
                message: 'Please provide a valid email address' 
            });
        }

        // Check if user already exists
        try {
            const existingUser = await database.getUserByEmail(email);

            if (existingUser) {
                return res.status(409).json({
                    message: 'User with this email already exists'
                });
            }

            // Create new user
            const user = await database.createUser({ name, email, password });

            // Generate JWT token
            const token = jwt.sign(
                { userId: user.id, email: user.email },
                JWT_SECRET,
                { expiresIn: '7d' }
            );

            res.status(201).json({
                message: 'User created successfully',
                token,
                user: {
                    id: user.id,
                    name: user.name,
                    email: user.email
                }
            });
        } catch (err) {
            return res.status(500).json({
                message: 'Internal server error'
            });
        }
    } catch (error) {
        res.status(500).json({
            message: 'Internal server error'
        });
    }
});

// Login user
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        // Validation
        if (!email || !password) {
            return res.status(400).json({ 
                message: 'Email and password are required' 
            });
        }

        // Find user by email
        try {
            const user = await database.getUserByEmail(email);

            if (!user) {
                return res.status(401).json({
                    message: 'Invalid email or password'
                });
            }

            // Verify password
            const isValidPassword = await bcrypt.compare(password, user.password_hash);
            if (!isValidPassword) {
                return res.status(401).json({
                    message: 'Invalid email or password'
                });
            }

            // Generate JWT token
            const token = jwt.sign(
                { userId: user.id, email: user.email },
                JWT_SECRET,
                { expiresIn: '7d' }
            );

            res.json({
                message: 'Login successful',
                token,
                user: {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    planType: user.plan_type
                }
            });
        } catch (err) {
            return res.status(500).json({
                message: 'Internal server error'
            });
        }
    } catch (error) {
        res.status(500).json({
            message: 'Internal server error'
        });
    }
});

// Verify token
router.post('/verify', (req, res) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');

        if (!token) {
            return res.status(401).json({ 
                message: 'No token provided' 
            });
        }

        jwt.verify(token, JWT_SECRET, async (err, decoded) => {
            if (err) {
                return res.status(401).json({
                    message: 'Invalid or expired token'
                });
            }

            // Get fresh user data
            try {
                const user = await database.getUserById(decoded.userId);

                if (!user) {
                    return res.status(401).json({
                        message: 'User not found'
                    });
                }

                res.json({
                    valid: true,
                    user: {
                        id: user.id,
                        name: user.name,
                        email: user.email,
                        planType: user.plan_type
                    }
                });
            } catch (err) {
                return res.status(500).json({
                    message: 'Internal server error'
                });
            }
        });
    } catch (error) {
        res.status(500).json({
            message: 'Internal server error'
        });
    }
});

// Refresh token
router.post('/refresh', (req, res) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');

        if (!token) {
            return res.status(401).json({ 
                message: 'No token provided' 
            });
        }

        jwt.verify(token, JWT_SECRET, (err, decoded) => {
            if (err) {
                return res.status(401).json({ 
                    message: 'Invalid or expired token' 
                });
            }

            // Generate new token
            const newToken = jwt.sign(
                { userId: decoded.userId, email: decoded.email },
                JWT_SECRET,
                { expiresIn: '7d' }
            );

            res.json({
                message: 'Token refreshed successfully',
                token: newToken
            });
        });
    } catch (error) {
        res.status(500).json({
            message: 'Internal server error'
        });
    }
});

// Logout (client-side token removal, but we can log it)
router.post('/logout', (req, res) => {
    // In a more sophisticated setup, you might want to blacklist the token
    // For now, we'll just acknowledge the logout
    res.json({ 
        message: 'Logout successful' 
    });
});

// Password reset request (placeholder for future implementation)
router.post('/forgot-password', (req, res) => {
    const { email } = req.body;

    if (!email) {
        return res.status(400).json({ 
            message: 'Email is required' 
        });
    }

    // TODO: Implement password reset functionality
    // This would typically involve:
    // 1. Generate a reset token
    // 2. Store it in the database with expiration
    // 3. Send email with reset link
    
    res.json({ 
        message: 'If an account with that email exists, a password reset link has been sent.' 
    });
});

// Change password (for authenticated users)
router.post('/change-password', authenticateToken, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.user.userId;

        if (!currentPassword || !newPassword) {
            return res.status(400).json({ 
                message: 'Current password and new password are required' 
            });
        }

        if (newPassword.length < 6) {
            return res.status(400).json({ 
                message: 'New password must be at least 6 characters long' 
            });
        }

        // Get user's current password hash
        database.getUserById(userId, async (err, user) => {
            if (err || !user) {
                return res.status(404).json({ 
                    message: 'User not found' 
                });
            }

            // Verify current password
            const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
            if (!isValidPassword) {
                return res.status(401).json({ 
                    message: 'Current password is incorrect' 
                });
            }

            // Hash new password
            const newPasswordHash = await bcrypt.hash(newPassword, 10);

            // Update password in database
            database.db.run(
                'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [newPasswordHash, userId],
                function(err) {
                    if (err) {
                        return res.status(500).json({
                            message: 'Failed to update password'
                        });
                    }

                    res.json({
                        message: 'Password updated successfully'
                    });
                }
            );
        });
    } catch (error) {
        res.status(500).json({
            message: 'Internal server error'
        });
    }
});

// Middleware to authenticate JWT token
function authenticateToken(req, res, next) {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
        return res.status(401).json({ 
            message: 'Access token required' 
        });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ 
                message: 'Invalid or expired token' 
            });
        }

        req.user = user;
        next();
    });
}

module.exports = router;
