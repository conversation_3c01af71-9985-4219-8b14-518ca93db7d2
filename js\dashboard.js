// Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

function initializeDashboard() {
    // Check authentication
    const token = localStorage.getItem('authToken');
    if (!token) {
        window.location.href = 'index.html';
        return;
    }
    
    // Load user data
    loadUserData();
    
    // Initialize navigation
    initializeNavigation();
    
    // Load dashboard data
    loadDashboardData();
    
    // Initialize event listeners
    initializeDashboardEvents();
}

function loadUserData() {
    const userData = JSON.parse(localStorage.getItem('userData') || '{}');
    const userNameElement = document.getElementById('userName');
    if (userNameElement && userData.name) {
        userNameElement.textContent = userData.name;
    }
}

function initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const sections = document.querySelectorAll('.content-section');
    
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetSection = this.dataset.section;
            
            // Update active nav item
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // Show target section
            sections.forEach(section => section.classList.remove('active'));
            const targetElement = document.getElementById(`${targetSection}-section`);
            if (targetElement) {
                targetElement.classList.add('active');
                updateSectionHeader(targetSection);
            }
        });
    });
}

function updateSectionHeader(section) {
    const titleElement = document.getElementById('sectionTitle');
    const subtitleElement = document.getElementById('sectionSubtitle');
    
    const sectionData = {
        overview: {
            title: 'Financial Overview',
            subtitle: 'Your complete financial picture at a glance'
        },
        accounts: {
            title: 'Account Management',
            subtitle: 'Monitor all your financial accounts in one place'
        },
        investments: {
            title: 'Investment Portfolio',
            subtitle: 'Track and optimize your investment performance'
        },
        goals: {
            title: 'Financial Goals',
            subtitle: 'Set, track, and achieve your financial objectives'
        },
        'ai-assistant': {
            title: 'AI Financial Coach',
            subtitle: 'Get personalized financial advice and insights'
        },
        scenarios: {
            title: 'Scenario Planning',
            subtitle: 'Explore different financial scenarios and outcomes'
        },
        settings: {
            title: 'Settings',
            subtitle: 'Manage your account and preferences'
        }
    };
    
    if (sectionData[section]) {
        titleElement.textContent = sectionData[section].title;
        subtitleElement.textContent = sectionData[section].subtitle;
    }
}

function initializeDashboardEvents() {
    // Sync accounts button
    const syncButton = document.querySelector('[onclick="syncAccounts()"]');
    if (syncButton) {
        syncButton.addEventListener('click', syncAccounts);
    }
    
    // Add goal form
    const addGoalForm = document.getElementById('addGoalForm');
    if (addGoalForm) {
        addGoalForm.addEventListener('submit', handleAddGoal);
    }
    
    // Chart period buttons
    document.querySelectorAll('.chart-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const period = this.dataset.period;
            updateChartPeriod(period);
            
            // Update active state
            document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

async function loadDashboardData() {
    try {
        // Load financial summary
        await loadFinancialSummary();
        
        // Load recent transactions
        await loadRecentTransactions();
        
        // Load goals
        await loadGoals();
        
        // Load AI insights
        await loadAIInsights();
        
    } catch (error) {
        WealthWiseApp.showNotification('Error loading dashboard data', 'error');
    }
}

async function loadFinancialSummary() {
    // Simulate API call - replace with actual API endpoint
    const mockData = {
        netWorth: 247850,
        monthlyIncome: 8500,
        monthlyExpenses: 4250,
        savingsRate: 50,
        netWorthChange: 12.5,
        incomeChange: 5.2,
        expenseChange: 8.1
    };
    
    // Update summary cards
    updateSummaryCards(mockData);
}

function updateSummaryCards(data) {
    const formatCurrency = (amount) => `$${amount.toLocaleString()}`;
    const formatPercentage = (percent) => `${percent}%`;
    
    // Update values
    document.querySelector('.summary-card:nth-child(1) .card-value').textContent = formatCurrency(data.netWorth);
    document.querySelector('.summary-card:nth-child(2) .card-value').textContent = formatCurrency(data.monthlyIncome);
    document.querySelector('.summary-card:nth-child(3) .card-value').textContent = formatCurrency(data.monthlyExpenses);
    document.querySelector('.summary-card:nth-child(4) .card-value').textContent = formatPercentage(data.savingsRate);
    
    // Update change indicators
    document.querySelector('.summary-card:nth-child(1) .card-change').innerHTML = 
        `<i class="fas fa-arrow-up"></i> +${data.netWorthChange}% this month`;
    document.querySelector('.summary-card:nth-child(2) .card-change').innerHTML = 
        `<i class="fas fa-arrow-up"></i> +${data.incomeChange}% vs last month`;
    document.querySelector('.summary-card:nth-child(3) .card-change').innerHTML = 
        `<i class="fas fa-arrow-down"></i> +${data.expenseChange}% vs last month`;
}

async function loadRecentTransactions() {
    // Simulate API call
    const mockTransactions = [
        {
            id: 1,
            name: 'Starbucks',
            category: 'Food & Dining',
            amount: -5.47,
            icon: 'fas fa-coffee',
            date: new Date()
        },
        {
            id: 2,
            name: 'Shell Gas Station',
            category: 'Transportation',
            amount: -45.20,
            icon: 'fas fa-gas-pump',
            date: new Date()
        },
        {
            id: 3,
            name: 'Salary Deposit',
            category: 'Income',
            amount: 4250.00,
            icon: 'fas fa-money-check-alt',
            date: new Date()
        }
    ];
    
    updateTransactionsList(mockTransactions);
}

function updateTransactionsList(transactions) {
    const transactionsList = document.querySelector('.transactions-list');
    if (!transactionsList) return;
    
    transactionsList.innerHTML = transactions.map(transaction => `
        <div class="transaction-item">
            <div class="transaction-icon">
                <i class="${transaction.icon}"></i>
            </div>
            <div class="transaction-details">
                <span class="transaction-name">${transaction.name}</span>
                <span class="transaction-category">${transaction.category}</span>
            </div>
            <div class="transaction-amount ${transaction.amount > 0 ? 'positive' : 'negative'}">
                ${transaction.amount > 0 ? '+' : ''}$${Math.abs(transaction.amount).toFixed(2)}
            </div>
        </div>
    `).join('');
}

async function loadGoals() {
    // Simulate API call
    const mockGoals = [
        {
            id: 1,
            name: 'Emergency Fund',
            target: 25000,
            current: 25000,
            status: 'completed',
            targetDate: '2023-12-01',
            achievedDate: '2023-11-01'
        },
        {
            id: 2,
            name: 'House Down Payment',
            target: 100000,
            current: 65000,
            status: 'in-progress',
            targetDate: '2025-12-01'
        },
        {
            id: 3,
            name: 'Retirement Fund',
            target: 1000000,
            current: 350000,
            status: 'in-progress',
            targetDate: '2055-01-01'
        }
    ];
    
    updateGoalsDisplay(mockGoals);
}

function updateGoalsDisplay(goals) {
    const goalsGrid = document.querySelector('.goals-grid');
    if (!goalsGrid) return;
    
    goalsGrid.innerHTML = goals.map(goal => {
        const progress = (goal.current / goal.target) * 100;
        const targetDate = new Date(goal.targetDate).toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short' 
        });
        
        return `
            <div class="goal-card">
                <div class="goal-header">
                    <h3>${goal.name}</h3>
                    <span class="goal-status ${goal.status}">${goal.status.replace('-', ' ')}</span>
                </div>
                <div class="goal-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                    <div class="progress-text">$${goal.current.toLocaleString()} / $${goal.target.toLocaleString()}</div>
                </div>
                <div class="goal-timeline">
                    Target: ${targetDate} • ${goal.status === 'completed' ? 'Achieved' : 'On track'}
                </div>
            </div>
        `;
    }).join('');
}

async function loadAIInsights() {
    // Simulate AI-generated insights
    const mockInsights = [
        {
            icon: 'fas fa-lightbulb',
            title: 'Optimize Your Savings',
            description: 'You could save an additional $320/month by reducing dining out expenses and switching to a high-yield savings account.',
            action: 'Take Action'
        },
        {
            icon: 'fas fa-chart-line',
            title: 'Investment Opportunity',
            description: 'Based on your risk profile, consider increasing your equity allocation by 10% to optimize long-term returns.',
            action: 'Learn More'
        },
        {
            icon: 'fas fa-shield-alt',
            title: 'Emergency Fund Status',
            description: 'Great job! Your emergency fund covers 6.2 months of expenses. Consider investing excess cash for better returns.',
            action: 'View Details'
        }
    ];
    
    updateInsightsDisplay(mockInsights);
}

function updateInsightsDisplay(insights) {
    const insightsGrid = document.querySelector('.insights-grid');
    if (!insightsGrid) return;
    
    insightsGrid.innerHTML = insights.map(insight => `
        <div class="insight-card">
            <div class="insight-icon">
                <i class="${insight.icon}"></i>
            </div>
            <div class="insight-content">
                <h4>${insight.title}</h4>
                <p>${insight.description}</p>
                <button class="insight-action">${insight.action}</button>
            </div>
        </div>
    `).join('');
}

// Event Handlers
function syncAccounts() {
    WealthWiseApp.showNotification('Syncing accounts...', 'info');
    
    // Simulate sync process
    setTimeout(() => {
        WealthWiseApp.showNotification('Accounts synced successfully!', 'success');
        loadDashboardData();
    }, 2000);
}

function showAddGoalModal() {
    document.getElementById('addGoalModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

async function handleAddGoal(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const goalData = {
        name: formData.get('goalName'),
        target: parseFloat(formData.get('goalAmount')),
        targetDate: formData.get('goalDate'),
        category: formData.get('goalCategory')
    };
    
    try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        WealthWiseApp.showNotification('Goal created successfully!', 'success');
        WealthWiseApp.closeModal('addGoalModal');
        e.target.reset();
        loadGoals();
        
    } catch (error) {
        WealthWiseApp.showNotification('Error creating goal', 'error');
    }
}

function generateInsights() {
    WealthWiseApp.showNotification('Generating new AI insights...', 'info');
    
    setTimeout(() => {
        loadAIInsights();
        WealthWiseApp.showNotification('New insights generated!', 'success');
    }, 2000);
}

function updateChartPeriod(period) {
    // This would update the charts with new data for the selected period
    WealthWiseApp.showNotification(`Chart updated for ${period} period`, 'info');
}

function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    window.location.href = 'index.html';
}

// Make functions globally available
window.syncAccounts = syncAccounts;
window.showAddGoalModal = showAddGoalModal;
window.generateInsights = generateInsights;
window.logout = logout;
