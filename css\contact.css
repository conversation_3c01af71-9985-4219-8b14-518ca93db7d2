/* Contact Page Specific Styles */

/* Hero Section */
.contact-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.contact-hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Contact Options Section */
.contact-options {
    padding: 80px 0;
    background: #f8fafc;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.contact-card {
    background: white;
    padding: 40px 30px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.contact-card.featured {
    border-color: #6366f1;
    transform: scale(1.02);
    position: relative;
}

.contact-card.featured::before {
    content: 'Most Popular';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #6366f1;
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 50%;
    margin: 0 auto 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.contact-card h3 {
    color: #1e293b;
    font-size: 1.5rem;
    margin-bottom: 16px;
    font-weight: 600;
}

.contact-card p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 24px;
}

.contact-details {
    margin-bottom: 24px;
    text-align: left;
}

.contact-details p {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    font-size: 0.95rem;
    color: #475569;
}

.contact-details i {
    color: #6366f1;
    width: 16px;
}

/* Contact Form Section */
.contact-form-section {
    padding: 80px 0;
    background: white;
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: #f8fafc;
    padding: 60px;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-header h2 {
    color: #1e293b;
    font-size: 2.5rem;
    margin-bottom: 16px;
    font-weight: 600;
}

.form-header p {
    color: #64748b;
    font-size: 1.125rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: #374151;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.form-group select {
    cursor: pointer;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
    accent-color: #6366f1;
}

.checkbox-label a {
    color: #6366f1;
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    width: 100%;
    margin-top: 16px;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: #f8fafc;
}

.faq-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #1e293b;
    margin-bottom: 60px;
    font-weight: 600;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.faq-item {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.faq-item h3 {
    color: #1e293b;
    font-size: 1.25rem;
    margin-bottom: 16px;
    font-weight: 600;
}

.faq-item p {
    color: #64748b;
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-hero h1 {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .contact-card.featured {
        transform: none;
    }
    
    .form-container {
        padding: 40px 30px;
        margin: 0 20px;
    }
    
    .form-header h2 {
        font-size: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .faq-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .faq-section h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .contact-hero {
        padding: 100px 0 60px;
    }
    
    .contact-hero h1 {
        font-size: 2rem;
    }
    
    .contact-options,
    .contact-form-section,
    .faq-section {
        padding: 60px 0;
    }
    
    .form-container {
        padding: 30px 20px;
        margin: 0 10px;
    }
    
    .contact-card {
        padding: 30px 20px;
    }
}
