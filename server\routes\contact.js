const express = require('express');
const router = express.Router();

// Use appropriate database based on configuration
const database = process.env.USE_SUPABASE === 'true' 
    ? require('../supabase') 
    : require('../database');

// Contact Sales endpoint
router.post('/sales', async (req, res) => {
    try {
        const { name, email, company, phone, employees, message, consent } = req.body;

        // Validation
        if (!name || !email || !company || !message) {
            return res.status(400).json({
                message: 'Name, email, company, and message are required'
            });
        }

        if (!consent) {
            return res.status(400).json({
                message: 'Consent is required to proceed'
            });
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                message: 'Please provide a valid email address'
            });
        }

        // Save contact request to database
        const contactData = {
            name,
            email,
            company,
            phone: phone || null,
            company_size: employees || null,
            message,
            consent: consent ? 1 : 0,
            status: 'new',
            created_at: new Date().toISOString()
        };

        try {
            const savedContact = await database.saveContactRequest(contactData);
            console.log('Contact request saved:', savedContact);
        } catch (dbError) {
            console.error('Failed to save contact request to database:', dbError);
            // Continue anyway - we don't want to fail the request if DB save fails
        }

        // In a real application, you would:
        // 1. Send an email to the sales team
        // 2. Add to CRM system
        // 3. Send confirmation email to the user
        // 4. Set up follow-up tasks

        // For now, we'll simulate this with logging
        console.log('📧 New sales contact request:', {
            name,
            email,
            company,
            phone,
            employees,
            message: message.substring(0, 100) + '...',
            timestamp: new Date().toISOString()
        });

        // Simulate email sending (in production, use SendGrid, AWS SES, etc.)
        await simulateEmailNotification(contactData);

        res.json({
            message: 'Thank you for your interest! Our sales team will contact you within 24 hours.',
            success: true
        });

    } catch (error) {
        console.error('Contact sales error:', error);
        res.status(500).json({
            message: 'Internal server error. Please try again or contact us directly.'
        });
    }
});

// Get contact requests (for admin/sales team)
router.get('/requests', async (req, res) => {
    try {
        // In a real app, this would require admin authentication
        const requests = await database.getContactRequests();
        res.json({ requests });
    } catch (error) {
        console.error('Error fetching contact requests:', error);
        res.status(500).json({
            message: 'Failed to fetch contact requests'
        });
    }
});

// Update contact request status
router.patch('/requests/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { status, notes } = req.body;

        const updatedRequest = await database.updateContactRequest(id, { status, notes });
        res.json({ 
            message: 'Contact request updated successfully',
            request: updatedRequest 
        });
    } catch (error) {
        console.error('Error updating contact request:', error);
        res.status(500).json({
            message: 'Failed to update contact request'
        });
    }
});

// Simulate email notification (replace with real email service)
async function simulateEmailNotification(contactData) {
    return new Promise((resolve) => {
        setTimeout(() => {
            console.log('📧 Email notifications sent:');
            console.log('   ✅ Sales team notified');
            console.log('   ✅ Confirmation sent to customer');
            console.log('   ✅ CRM updated');
            resolve();
        }, 100);
    });
}

// General contact form endpoint
router.post('/general', async (req, res) => {
    try {
        const { firstName, lastName, email, phone, company, inquiryType, subject, message, newsletter, privacy } = req.body;

        // Validation
        if (!firstName || !lastName || !email || !inquiryType || !subject || !message || !privacy) {
            return res.status(400).json({
                message: 'Please fill in all required fields and accept the privacy policy.'
            });
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                message: 'Please enter a valid email address.'
            });
        }

        // Create contact record
        const contactData = {
            name: `${firstName} ${lastName}`,
            email,
            phone: phone || null,
            company: company || null,
            inquiry_type: inquiryType,
            subject,
            message,
            newsletter_consent: newsletter || false,
            privacy_consent: privacy,
            status: 'new',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        if (process.env.USE_SUPABASE === 'true') {
            // Supabase implementation
            const { createClient } = require('@supabase/supabase-js');
            const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

            const { data, error } = await supabase
                .from('general_contacts')
                .insert([contactData]);

            if (error) {
                console.error('Supabase error:', error);
                return res.status(500).json({ message: 'Failed to save contact information' });
            }
        } else {
            // SQLite implementation
            const db = require('../database');

            // Create table if it doesn't exist
            db.run(`
                CREATE TABLE IF NOT EXISTS general_contacts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    email TEXT NOT NULL,
                    phone TEXT,
                    company TEXT,
                    inquiry_type TEXT NOT NULL,
                    subject TEXT NOT NULL,
                    message TEXT NOT NULL,
                    newsletter_consent BOOLEAN DEFAULT FALSE,
                    privacy_consent BOOLEAN NOT NULL,
                    status TEXT DEFAULT 'new',
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // Insert contact
            db.run(`
                INSERT INTO general_contacts (
                    name, email, phone, company, inquiry_type, subject, message,
                    newsletter_consent, privacy_consent, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                contactData.name,
                contactData.email,
                contactData.phone,
                contactData.company,
                contactData.inquiry_type,
                contactData.subject,
                contactData.message,
                contactData.newsletter_consent,
                contactData.privacy_consent,
                contactData.status,
                contactData.created_at,
                contactData.updated_at
            ]);
        }

        // Simulate email notifications
        console.log('📧 Email Notifications Sent:');
        console.log(`   → Support Team: New ${inquiryType} inquiry from ${contactData.name}`);
        console.log(`   → Customer: Thank you for contacting WealthWise AI`);
        console.log(`   → CRM: Contact record created for follow-up`);

        res.json({
            message: 'Thank you for your message! We\'ll get back to you within 24 hours.',
            contactId: Date.now() // Simulated ID
        });

    } catch (error) {
        console.error('General contact error:', error);
        res.status(500).json({
            message: 'Failed to send message. Please try again.'
        });
    }
});

module.exports = router;
