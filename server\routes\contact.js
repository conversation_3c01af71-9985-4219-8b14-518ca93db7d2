const express = require('express');
const router = express.Router();

// Use appropriate database based on configuration
const database = process.env.USE_SUPABASE === 'true' 
    ? require('../supabase') 
    : require('../database');

// Contact Sales endpoint
router.post('/sales', async (req, res) => {
    try {
        const { name, email, company, phone, employees, message, consent } = req.body;

        // Validation
        if (!name || !email || !company || !message) {
            return res.status(400).json({
                message: 'Name, email, company, and message are required'
            });
        }

        if (!consent) {
            return res.status(400).json({
                message: 'Consent is required to proceed'
            });
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                message: 'Please provide a valid email address'
            });
        }

        // Save contact request to database
        const contactData = {
            name,
            email,
            company,
            phone: phone || null,
            company_size: employees || null,
            message,
            consent: consent ? 1 : 0,
            status: 'new',
            created_at: new Date().toISOString()
        };

        try {
            const savedContact = await database.saveContactRequest(contactData);
            console.log('Contact request saved:', savedContact);
        } catch (dbError) {
            console.error('Failed to save contact request to database:', dbError);
            // Continue anyway - we don't want to fail the request if DB save fails
        }

        // In a real application, you would:
        // 1. Send an email to the sales team
        // 2. Add to CRM system
        // 3. Send confirmation email to the user
        // 4. Set up follow-up tasks

        // For now, we'll simulate this with logging
        console.log('📧 New sales contact request:', {
            name,
            email,
            company,
            phone,
            employees,
            message: message.substring(0, 100) + '...',
            timestamp: new Date().toISOString()
        });

        // Simulate email sending (in production, use SendGrid, AWS SES, etc.)
        await simulateEmailNotification(contactData);

        res.json({
            message: 'Thank you for your interest! Our sales team will contact you within 24 hours.',
            success: true
        });

    } catch (error) {
        console.error('Contact sales error:', error);
        res.status(500).json({
            message: 'Internal server error. Please try again or contact us directly.'
        });
    }
});

// Get contact requests (for admin/sales team)
router.get('/requests', async (req, res) => {
    try {
        // In a real app, this would require admin authentication
        const requests = await database.getContactRequests();
        res.json({ requests });
    } catch (error) {
        console.error('Error fetching contact requests:', error);
        res.status(500).json({
            message: 'Failed to fetch contact requests'
        });
    }
});

// Update contact request status
router.patch('/requests/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { status, notes } = req.body;

        const updatedRequest = await database.updateContactRequest(id, { status, notes });
        res.json({ 
            message: 'Contact request updated successfully',
            request: updatedRequest 
        });
    } catch (error) {
        console.error('Error updating contact request:', error);
        res.status(500).json({
            message: 'Failed to update contact request'
        });
    }
});

// Simulate email notification (replace with real email service)
async function simulateEmailNotification(contactData) {
    return new Promise((resolve) => {
        setTimeout(() => {
            console.log('📧 Email notifications sent:');
            console.log('   ✅ Sales team notified');
            console.log('   ✅ Confirmation sent to customer');
            console.log('   ✅ CRM updated');
            resolve();
        }, 100);
    });
}

module.exports = router;
