<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Functionality Test - WealthWise AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .test-button:hover {
            background: #5855eb;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <h1>🧪 WealthWise AI - Button Functionality Test</h1>
    <p>This page tests all the button functionality to ensure everything is working correctly.</p>

    <div class="test-section">
        <h2>🔐 Authentication Tests</h2>
        <button class="test-button" onclick="testLogin()">Test Login Modal</button>
        <button class="test-button" onclick="testSignup()">Test Signup Modal</button>
        <button class="test-button" onclick="testFreeTrial()">Test Free Trial</button>
        <div id="auth-status" class="status info">Click buttons above to test authentication modals</div>
    </div>

    <div class="test-section">
        <h2>📞 Contact Tests</h2>
        <button class="test-button" onclick="testContactSales()">Test Contact Sales Modal</button>
        <button class="test-button" onclick="testContactForm()">Test Contact Form Submission</button>
        <div id="contact-status" class="status info">Click buttons above to test contact functionality</div>
    </div>

    <div class="test-section">
        <h2>🤖 AI Tests</h2>
        <button class="test-button" onclick="testAIChat()">Test AI Chat</button>
        <button class="test-button" onclick="testInsights()">Test Financial Insights</button>
        <button class="test-button" onclick="testScenario()">Test Scenario Analysis</button>
        <div id="ai-status" class="status info">Click buttons above to test AI functionality</div>
    </div>

    <div class="test-section">
        <h2>🌐 Navigation Tests</h2>
        <button class="test-button" onclick="testNavigation()">Test All Navigation Links</button>
        <div id="nav-status" class="status info">Click button above to test navigation</div>
    </div>

    <div class="test-section">
        <h2>📊 Test Results Summary</h2>
        <div id="summary" class="status info">Run tests above to see results</div>
    </div>

    <script>
        let testResults = {
            auth: 0,
            contact: 0,
            ai: 0,
            navigation: 0,
            total: 0
        };

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.innerHTML = message;
        }

        function updateSummary() {
            const total = testResults.auth + testResults.contact + testResults.ai + testResults.navigation;
            testResults.total = total;
            
            const summary = `
                <h3>Test Results:</h3>
                <p>✅ Authentication: ${testResults.auth}/3 tests passed</p>
                <p>✅ Contact: ${testResults.contact}/2 tests passed</p>
                <p>✅ AI: ${testResults.ai}/3 tests passed</p>
                <p>✅ Navigation: ${testResults.navigation}/1 tests passed</p>
                <p><strong>Total: ${total}/9 tests passed</strong></p>
                ${total === 9 ? '<p style="color: green; font-weight: bold;">🎉 All tests passed!</p>' : '<p style="color: orange;">⚠️ Some tests need attention</p>'}
            `;
            
            updateStatus('summary', summary, total === 9 ? 'success' : 'info');
        }

        // Authentication Tests
        function testLogin() {
            try {
                if (typeof showLoginModal === 'function') {
                    showLoginModal();
                    updateStatus('auth-status', '✅ Login modal function exists and executed', 'success');
                    testResults.auth++;
                } else {
                    updateStatus('auth-status', '❌ showLoginModal function not found', 'error');
                }
            } catch (error) {
                updateStatus('auth-status', `❌ Login test failed: ${error.message}`, 'error');
            }
            updateSummary();
        }

        function testSignup() {
            try {
                if (typeof showSignupModal === 'function') {
                    showSignupModal();
                    updateStatus('auth-status', '✅ Signup modal function exists and executed', 'success');
                    testResults.auth++;
                } else {
                    updateStatus('auth-status', '❌ showSignupModal function not found', 'error');
                }
            } catch (error) {
                updateStatus('auth-status', `❌ Signup test failed: ${error.message}`, 'error');
            }
            updateSummary();
        }

        function testFreeTrial() {
            try {
                if (typeof startFreeTrial === 'function') {
                    startFreeTrial();
                    updateStatus('auth-status', '✅ Free trial function exists and executed', 'success');
                    testResults.auth++;
                } else {
                    updateStatus('auth-status', '❌ startFreeTrial function not found', 'error');
                }
            } catch (error) {
                updateStatus('auth-status', `❌ Free trial test failed: ${error.message}`, 'error');
            }
            updateSummary();
        }

        // Contact Tests
        function testContactSales() {
            try {
                if (typeof showContactSalesModal === 'function') {
                    showContactSalesModal();
                    updateStatus('contact-status', '✅ Contact sales modal function exists and executed', 'success');
                    testResults.contact++;
                } else {
                    updateStatus('contact-status', '❌ showContactSalesModal function not found', 'error');
                }
            } catch (error) {
                updateStatus('contact-status', `❌ Contact sales test failed: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testContactForm() {
            try {
                const response = await fetch('/api/contact/sales', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: 'Test User',
                        email: '<EMAIL>',
                        company: 'Test Company',
                        message: 'This is a test message',
                        consent: true
                    })
                });

                if (response.ok) {
                    updateStatus('contact-status', '✅ Contact form API working correctly', 'success');
                    testResults.contact++;
                } else {
                    updateStatus('contact-status', `❌ Contact form API failed: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus('contact-status', `❌ Contact form test failed: ${error.message}`, 'error');
            }
            updateSummary();
        }

        // AI Tests
        async function testAIChat() {
            try {
                // First try to login
                const loginResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'demo123'
                    })
                });

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    const token = loginData.token;

                    const chatResponse = await fetch('/api/ai/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify({
                            message: 'Test message'
                        })
                    });

                    if (chatResponse.ok) {
                        updateStatus('ai-status', '✅ AI Chat API working correctly', 'success');
                        testResults.ai++;
                    } else {
                        updateStatus('ai-status', `❌ AI Chat API failed: ${chatResponse.status}`, 'error');
                    }
                } else {
                    updateStatus('ai-status', '❌ Could not login for AI test', 'error');
                }
            } catch (error) {
                updateStatus('ai-status', `❌ AI Chat test failed: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testInsights() {
            try {
                const loginResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'demo123'
                    })
                });

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    const token = loginData.token;

                    const insightsResponse = await fetch('/api/ai/insights/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (insightsResponse.ok) {
                        updateStatus('ai-status', '✅ Financial Insights API working correctly', 'success');
                        testResults.ai++;
                    } else {
                        updateStatus('ai-status', `❌ Financial Insights API failed: ${insightsResponse.status}`, 'error');
                    }
                } else {
                    updateStatus('ai-status', '❌ Could not login for insights test', 'error');
                }
            } catch (error) {
                updateStatus('ai-status', `❌ Insights test failed: ${error.message}`, 'error');
            }
            updateSummary();
        }

        async function testScenario() {
            try {
                const loginResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'demo123'
                    })
                });

                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    const token = loginData.token;

                    const scenarioResponse = await fetch('/api/ai/scenario/analyze', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify({
                            scenario: {
                                type: 'test',
                                name: 'Test Scenario'
                            }
                        })
                    });

                    if (scenarioResponse.ok) {
                        updateStatus('ai-status', '✅ Scenario Analysis API working correctly', 'success');
                        testResults.ai++;
                    } else {
                        updateStatus('ai-status', `❌ Scenario Analysis API failed: ${scenarioResponse.status}`, 'error');
                    }
                } else {
                    updateStatus('ai-status', '❌ Could not login for scenario test', 'error');
                }
            } catch (error) {
                updateStatus('ai-status', `❌ Scenario test failed: ${error.message}`, 'error');
            }
            updateSummary();
        }

        // Navigation Tests
        function testNavigation() {
            try {
                const links = [
                    { url: '/', name: 'Home' },
                    { url: '/about.html', name: 'About' },
                    { url: '/api/health', name: 'Health Check' }
                ];

                let passedTests = 0;
                let totalTests = links.length;

                links.forEach(async (link, index) => {
                    try {
                        const response = await fetch(link.url);
                        if (response.ok) {
                            passedTests++;
                        }
                        
                        if (index === totalTests - 1) {
                            if (passedTests === totalTests) {
                                updateStatus('nav-status', `✅ All ${totalTests} navigation links working`, 'success');
                                testResults.navigation = 1;
                            } else {
                                updateStatus('nav-status', `⚠️ ${passedTests}/${totalTests} navigation links working`, 'error');
                            }
                            updateSummary();
                        }
                    } catch (error) {
                        updateStatus('nav-status', `❌ Navigation test failed: ${error.message}`, 'error');
                        updateSummary();
                    }
                });
            } catch (error) {
                updateStatus('nav-status', `❌ Navigation test failed: ${error.message}`, 'error');
                updateSummary();
            }
        }

        // Load required scripts
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // Initialize
        window.addEventListener('load', async () => {
            try {
                await loadScript('/js/app.js');
                await loadScript('/js/auth.js');
                updateStatus('summary', '✅ Scripts loaded successfully. Ready to run tests!', 'success');
            } catch (error) {
                updateStatus('summary', `❌ Failed to load scripts: ${error.message}`, 'error');
            }
        });
    </script>
</body>
</html>
